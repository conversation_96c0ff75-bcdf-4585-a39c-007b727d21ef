<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能向导弹窗测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .modal-backdrop {
            transition: opacity 0.3s ease;
        }
        .modal-content {
            transition: transform 0.3s ease;
        }
    </style>
</head>
<body class="bg-gray-900 text-white">
    <div class="container mx-auto p-8">
        <h1 class="text-3xl font-bold mb-8">智能向导弹窗测试</h1>
        
        <div class="space-x-4">
            <button id="start-wizard-btn" 
                    class="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded-lg flex items-center">
                <i class="fas fa-magic mr-2"></i> 智能向导
            </button>
            
            <button onclick="testOpenModal()" 
                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg">
                直接测试弹窗
            </button>
        </div>
        
        <div class="mt-8">
            <h2 class="text-xl font-bold mb-4">调试信息</h2>
            <div id="debug-info" class="bg-gray-800 p-4 rounded-lg text-sm font-mono"></div>
        </div>
    </div>

    <!-- Intelligent Workflow Wizard Modal -->
    <div id="workflow-wizard-modal"
        class="modal-backdrop fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center hidden opacity-0">
        <div class="modal-content bg-gray-800 rounded-lg shadow-xl w-full max-w-6xl h-5/6 transform scale-95 flex flex-col">
            <!-- Header -->
            <div class="flex justify-between items-center p-6 border-b border-gray-700">
                <h2 class="text-2xl font-bold text-white">智能规则创建向导</h2>
                <button onclick="closeWorkflowWizard()" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            
            <!-- Progress Bar -->
            <div class="px-6 py-4 border-b border-gray-700">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm text-gray-300">进度</span>
                    <span id="workflow-progress-text" class="text-sm text-gray-300">0/5</span>
                </div>
                <div class="w-full bg-gray-700 rounded-full h-2">
                    <div id="workflow-progress-bar" class="bg-purple-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                </div>
                <div class="flex justify-between mt-2 text-xs text-gray-400">
                    <span>规则属性</span>
                    <span>模板选择</span>
                    <span>参数填写</span>
                    <span>SQL预览</span>
                    <span>规则创建</span>
                </div>
            </div>
            
            <!-- Content Area -->
            <div class="flex-1 flex overflow-hidden">
                <!-- Step Content -->
                <div class="flex-1 p-6 overflow-y-auto">
                    <div id="workflow-content">
                        <h3 class="text-lg font-bold mb-4">测试内容</h3>
                        <p class="text-gray-300">这是智能向导的测试内容。</p>
                    </div>
                </div>
                
                <!-- Summary Panel -->
                <div class="w-80 bg-gray-900 p-6 border-l border-gray-700 overflow-y-auto">
                    <h3 class="text-lg font-semibold text-white mb-4">规则摘要</h3>
                    <div id="workflow-summary" class="space-y-3 text-sm">
                        <div class="text-gray-400">开始创建规则...</div>
                    </div>
                </div>
            </div>
            
            <!-- Footer -->
            <div class="flex justify-between items-center p-6 border-t border-gray-700">
                <button id="workflow-prev-btn" onclick="workflowPrevStep()" 
                    class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg flex items-center" disabled>
                    <i class="fas fa-arrow-left mr-2"></i> 上一步
                </button>
                <div class="flex space-x-2">
                    <button id="workflow-cancel-btn" onclick="closeWorkflowWizard()" 
                        class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg">
                        取消
                    </button>
                    <button id="workflow-next-btn" onclick="workflowNextStep()" 
                        class="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded-lg flex items-center">
                        下一步 <i class="fas fa-arrow-right ml-2"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentWorkflowStep = 0;
        const workflowSteps = ['rule_attributes', 'template_selection', 'parameter_input', 'sql_preview', 'rule_creation'];
        
        function log(message) {
            const debugInfo = document.getElementById('debug-info');
            const timestamp = new Date().toLocaleTimeString();
            debugInfo.innerHTML += `[${timestamp}] ${message}<br>`;
            debugInfo.scrollTop = debugInfo.scrollHeight;
            console.log(message);
        }

        function openModal(modalId) {
            log(`Opening modal: ${modalId}`);
            const modal = document.getElementById(modalId);
            if (!modal) {
                log(`ERROR: Modal with id '${modalId}' not found`);
                return;
            }
            
            log(`Modal found, current classes: ${modal.className}`);
            
            // Show the modal
            modal.classList.remove('hidden');
            modal.style.display = 'flex';
            
            // Force a reflow
            modal.offsetHeight;
            
            // Apply the show animation
            setTimeout(() => {
                const modalContent = modal.querySelector('.modal-content');
                if (modalContent) {
                    modalContent.style.transform = 'scale(1)';
                    log(`Modal content animation applied`);
                }
                modal.style.opacity = '1';
                log(`Modal opened successfully: ${modalId}`);
            }, 10);
        }

        function closeModal(modalId) {
            log(`Closing modal: ${modalId}`);
            const modal = document.getElementById(modalId);
            if (!modal) {
                log(`ERROR: Modal with id '${modalId}' not found`);
                return;
            }
            
            const modalContent = modal.querySelector('.modal-content');
            if (modalContent) {
                modalContent.style.transform = 'scale(0.95)';
            }
            modal.style.opacity = '0';
            
            setTimeout(() => {
                modal.classList.add('hidden');
                modal.style.display = 'none';
                log(`Modal closed: ${modalId}`);
            }, 300);
        }

        function initializeWorkflowWizard() {
            log('Initializing workflow wizard');
            currentWorkflowStep = 0;
            updateWorkflowProgress();
            log('Workflow wizard initialized');
        }

        function updateWorkflowProgress() {
            const progress = ((currentWorkflowStep + 1) / workflowSteps.length) * 100;
            const progressBar = document.getElementById('workflow-progress-bar');
            const progressText = document.getElementById('workflow-progress-text');
            
            if (progressBar) progressBar.style.width = progress + '%';
            if (progressText) progressText.textContent = `${currentWorkflowStep + 1}/${workflowSteps.length}`;
            
            log(`Progress updated: ${currentWorkflowStep + 1}/${workflowSteps.length} (${progress.toFixed(1)}%)`);
        }

        function closeWorkflowWizard() {
            log('Closing workflow wizard');
            closeModal('workflow-wizard-modal');
            currentWorkflowStep = 0;
        }

        function workflowPrevStep() {
            log('Previous step clicked');
        }

        function workflowNextStep() {
            log('Next step clicked');
        }

        function testOpenModal() {
            log('Direct test button clicked');
            openModal('workflow-wizard-modal');
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            log('DOM loaded, initializing...');
            
            const startWizardBtn = document.getElementById('start-wizard-btn');
            if (startWizardBtn) {
                log('Start wizard button found, adding event listener');
                startWizardBtn.addEventListener('click', function() {
                    log('Start wizard button clicked');
                    openModal('workflow-wizard-modal');
                    initializeWorkflowWizard();
                });
            } else {
                log('ERROR: Start wizard button not found!');
            }
            
            log('Initialization complete');
        });
    </script>
</body>
</html>
