<!DOCTYPE html>
<html lang="en" class="dark">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>临时规则编写工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/atom-one-dark.min.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'gray-900': '#121212',
                        'gray-800': '#1e1e1e',
                        'gray-700': '#2d2d2d',
                        'gray-600': '#444444',
                    }
                }
            }
        }
    </script>
    <style>
        /* For modal transitions */
        .modal-backdrop {
            transition: opacity 0.3s ease;
        }

        .modal-content {
            transition: transform 0.3s ease;
        }
    </style>
</head>

<body class="bg-gray-900 text-gray-300 font-sans p-8">

    <!-- Main Container -->
    <div class="container mx-auto space-y-8">

        <!-- Part 1: SQL Generator -->
        <div class="bg-gray-800 p-6 rounded-lg shadow-lg">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-bold text-white"><i class="fas fa-magic-wand-sparkles mr-2"></i>规则SQL生成器</h1>
                <div class="flex space-x-2">
                    <button id="start-wizard-btn"
                        class="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded-lg flex items-center">
                        <i class="fas fa-magic mr-2"></i> 智能向导
                    </button>
                    <button onclick="openModal('db-config-modal')"
                        class="bg-gray-700 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg flex items-center">
                        <i class="fas fa-database mr-2"></i> 数据库参数配置
                    </button>
                </div>
            </div>

            <div class="flex space-x-8">
                <!-- Left Panel: Form -->
                <div class="w-1/2 space-y-6">
                    <div>
                        <label for="rule-name" class="block mb-2 text-sm font-medium text-gray-300">规则名称</label>
                        <input type="text" id="rule-name"
                            class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                            placeholder="例如：限定支付-限单人多次">
                    </div>

                    <!-- Rule Attributes Section -->
                    <div class="border border-gray-600 rounded-lg p-4 mb-4">
                        <h3 class="text-lg font-semibold text-white mb-4">规则属性</h3>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label for="rule-type" class="block mb-2 text-sm font-medium text-gray-300">规则类型</label>
                                <select id="rule-type"
                                    class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                    <option value="">自动检测</option>
                                </select>
                            </div>
                            <div>
                                <label for="patient-type" class="block mb-2 text-sm font-medium text-gray-300">适用范围</label>
                                <select id="patient-type"
                                    class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                    <option value="">自动检测</option>
                                    <option value="inpatient">住院</option>
                                    <option value="outpatient">门诊</option>
                                    <option value="general">通用</option>
                                </select>
                            </div>
                            <div>
                                <label for="match-method" class="block mb-2 text-sm font-medium text-gray-300">匹配方式</label>
                                <select id="match-method"
                                    class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                    <option value="">自动检测</option>
                                    <option value="name">按名称</option>
                                    <option value="code">按编码</option>
                                </select>
                            </div>
                            <div>
                                <label for="database-type" class="block mb-2 text-sm font-medium text-gray-300">数据库类型</label>
                                <select id="database-type"
                                    class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                    <option value="postgresql">PostgreSQL</option>
                                    <option value="oracle">Oracle</option>
                                </select>
                            </div>
                        </div>
                        <div class="mt-4 flex space-x-2">
                            <button id="analyze-rule-btn"
                                class="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded-lg flex items-center text-sm">
                                <i class="fas fa-magic mr-2"></i> 智能分析
                            </button>
                            <button id="get-recommendations-btn"
                                class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg flex items-center text-sm">
                                <i class="fas fa-lightbulb mr-2"></i> 获取推荐
                            </button>
                        </div>
                    </div>

                    <!-- Template Selection Section -->
                    <div class="border border-gray-600 rounded-lg p-4 mb-4">
                        <h3 class="text-lg font-semibold text-white mb-4">模板选择</h3>
                        <div id="template-recommendations" class="space-y-2 mb-4">
                            <p class="text-gray-500 text-sm">点击"获取推荐"按钮获取智能模板推荐</p>
                        </div>
                        <div>
                            <label for="rule-template" class="block mb-2 text-sm font-medium text-gray-300">或手动选择模板</label>
                            <select id="rule-template"
                                class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                <option value="">选择一个模板</option>
                            </select>
                        </div>
                    </div>

                    <div class="border-t border-gray-700 pt-6">
                        <h3 class="text-lg font-semibold text-white mb-4">规则条件 (由模板生成)</h3>
                        <div class="space-y-4" id="conditions-container">
                            <p class="text-gray-500">请先选择一个SQL模板</p>
                        </div>
                    </div>
                </div>

                <!-- Right Panel: SQL Editor -->
                <div class="w-1/2 flex flex-col">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-semibold text-white">SQL编辑器</h2>
                        <div class="flex space-x-2">
                            <button id="toggle-edit-mode"
                                class="bg-gray-700 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg flex items-center text-sm">
                                <i class="fas fa-edit mr-2"></i> <span id="edit-mode-text">编辑模式</span>
                            </button>
                            <button id="copy-sql"
                                class="bg-gray-700 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg flex items-center text-sm">
                                <i class="fas fa-copy mr-2"></i> 复制
                            </button>
                        </div>
                    </div>
                    <div class="bg-gray-900 rounded-lg flex-grow p-1 relative">
                        <!-- 预览模式 -->
                        <pre id="sql-preview" class="language-sql h-full block p-4 overflow-auto"
                            style="margin: 0;"><code id="sql-output" class="language-sql">-- 在左侧表单中填写信息以生成SQL...</code></pre>

                        <!-- 编辑模式 -->
                        <textarea id="sql-editor"
                            class="w-full h-full bg-gray-900 text-gray-300 font-mono text-sm p-4 border-none outline-none resize-none"
                            style="display: none; min-height: 300px;"
                            placeholder="-- 在此处编写或编辑SQL代码...">-- 在左侧表单中填写信息以生成SQL...</textarea>
                    </div>
                    <div class="mt-6 flex justify-between">
                        <button id="new-rule-btn"
                            class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-3 px-6 rounded-lg flex items-center">
                            <i class="fas fa-plus mr-2"></i> 新建规则
                        </button>
                        <button id="save-rule-btn"
                            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg flex items-center">
                            <i class="fas fa-check-circle mr-2"></i> 保存规则
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Part 2: Rule Management -->
        <div class="bg-gray-800 p-6 rounded-lg shadow-lg">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-bold text-white"><i class="fas fa-tasks mr-2"></i>规则管理</h1>
            </div>
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left text-gray-400">
                    <thead class="text-xs text-gray-300 uppercase bg-gray-700">
                        <tr>
                            <th scope="col" class="px-6 py-3">规则名称</th>
                            <th scope="col" class="px-6 py-3">来源</th>
                            <th scope="col" class="px-6 py-3">状态</th>
                            <th scope="col" class="px-6 py-3">创建日期</th>
                            <th scope="col" class="px-6 py-3 text-center">操作</th>
                        </tr>
                    </thead>
                    <tbody id="rules-table-body">
                        <!-- Rules will be loaded here dynamically -->
                    </tbody>
                </table>
            </div>
        </div>

    </div>    <
!-- DB Config Modal -->
    <div id="db-config-modal"
        class="modal-backdrop fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center hidden">
        <div class="modal-content bg-gray-800 rounded-lg shadow-xl p-8 w-full max-w-md transform scale-95">
            <h2 class="text-2xl font-bold text-white mb-6">数据库配置</h2>
            <div class="space-y-4">
                <div>
                    <label for="db-type" class="block mb-2 text-sm font-medium text-gray-300">数据库类型</label>
                    <select id="db-type"
                        class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                        <option>PostgreSQL</option>
                        <option>Oracle</option>
                    </select>
                </div>
                <div>
                    <label for="db-host" class="block mb-2 text-sm font-medium text-gray-300">主机</label>
                    <input type="text" id="db-host"
                        class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg block w-full p-2.5"
                        placeholder="localhost">
                </div>
                <div>
                    <label for="db-port" class="block mb-2 text-sm font-medium text-gray-300">端口</label>
                    <input type="text" id="db-port"
                        class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg block w-full p-2.5"
                        placeholder="5432">
                </div>
                <div>
                    <label for="db-user" class="block mb-2 text-sm font-medium text-gray-300">用户名</label>
                    <input type="text" id="db-user"
                        class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg block w-full p-2.5"
                        placeholder="admin">
                </div>
                <div>
                    <label for="db-pass" class="block mb-2 text-sm font-medium text-gray-300">密码</label>
                    <input type="password" id="db-pass"
                        class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg block w-full p-2.5">
                </div>
            </div>
            <div class="flex justify-end space-x-4 mt-8">
                <button onclick="closeModal('db-config-modal')"
                    class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg">取消</button>
                <button class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg">保存</button>
            </div>
        </div>
    </div>

    <!-- Search Modal -->
    <div id="search-modal"
        class="modal-backdrop fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center hidden">
        <div class="modal-content bg-gray-800 rounded-lg shadow-xl p-8 w-full max-w-2xl transform scale-95">
            <h2 class="text-2xl font-bold text-white mb-6">从[医保三目表]检索</h2>
            <input type="text"
                class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg block w-full p-2.5 mb-4"
                placeholder="输入医保编码或名称...">
            <div class="max-h-80 overflow-y-auto">
                <table class="w-full text-sm text-left text-gray-400">
                    <thead class="text-xs text-gray-300 uppercase bg-gray-700 sticky top-0">
                        <tr>
                            <th class="px-4 py-2">医保编码</th>
                            <th class="px-4 py-2">医保名称</th>
                            <th class="px-4 py-2">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="border-b border-gray-700">
                            <td>P001</td>
                            <td>项目一</td>
                            <td><button class="text-blue-500">选择</button></td>
                        </tr>
                        <tr class="border-b border-gray-700">
                            <td>P002</td>
                            <td>项目二</td>
                            <td><button class="text-blue-500">选择</button></td>
                        </tr>
                        <tr class="border-b border-gray-700">
                            <td>D005</td>
                            <td>药品A</td>
                            <td><button class="text-blue-500">选择</button></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="flex justify-end mt-8">
                <button onclick="closeModal('search-modal')"
                    class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg">关闭</button>
            </div>
        </div>
    </div>

    <!-- Intelligent Workflow Wizard Modal -->
    <div id="workflow-wizard-modal"
        class="modal-backdrop fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center hidden">
        <div class="modal-content bg-gray-800 rounded-lg shadow-xl w-full max-w-6xl h-5/6 transform scale-95 flex flex-col">
            <!-- Header -->
            <div class="flex justify-between items-center p-6 border-b border-gray-700">
                <h2 class="text-2xl font-bold text-white">智能规则创建向导</h2>
                <button onclick="closeWorkflowWizard()" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            
            <!-- Progress Bar -->
            <div class="px-6 py-4 border-b border-gray-700">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm text-gray-300">进度</span>
                    <span id="workflow-progress-text" class="text-sm text-gray-300">0/5</span>
                </div>
                <div class="w-full bg-gray-700 rounded-full h-2">
                    <div id="workflow-progress-bar" class="bg-purple-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                </div>
                <div class="flex justify-between mt-2 text-xs text-gray-400">
                    <span>规则属性</span>
                    <span>模板选择</span>
                    <span>参数填写</span>
                    <span>SQL预览</span>
                    <span>规则创建</span>
                </div>
            </div>
            
            <!-- Content Area -->
            <div class="flex-1 flex overflow-hidden">
                <!-- Step Content -->
                <div class="flex-1 p-6 overflow-y-auto">
                    <div id="workflow-content">
                        <!-- Dynamic content will be loaded here -->
                    </div>
                </div>
                
                <!-- Summary Panel -->
                <div class="w-80 bg-gray-900 p-6 border-l border-gray-700 overflow-y-auto">
                    <h3 class="text-lg font-semibold text-white mb-4">规则摘要</h3>
                    <div id="workflow-summary" class="space-y-3 text-sm">
                        <div class="text-gray-400">开始创建规则...</div>
                    </div>
                </div>
            </div>
            
            <!-- Footer -->
            <div class="flex justify-between items-center p-6 border-t border-gray-700">
                <button id="workflow-prev-btn" onclick="workflowPrevStep()" 
                    class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg flex items-center" disabled>
                    <i class="fas fa-arrow-left mr-2"></i> 上一步
                </button>
                <div class="flex space-x-2">
                    <button id="workflow-cancel-btn" onclick="closeWorkflowWizard()" 
                        class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg">
                        取消
                    </button>
                    <button id="workflow-next-btn" onclick="workflowNextStep()" 
                        class="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded-lg flex items-center">
                        下一步 <i class="fas fa-arrow-right ml-2"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- Global Variables ---
        let sqlTemplates = {};
        let isEditMode = false;
        let isEditingExistingRule = false;
        let originalRuleName = null;
        let currentWorkflow = null;
        let currentWorkflowStep = 0;
        const workflowSteps = [
            'rule_attributes',
            'template_selection', 
            'parameter_input',
            'sql_preview',
            'rule_creation'
        ];

        // --- DOM Elements ---
        const ruleTemplateSelect = document.getElementById('rule-template');
        const conditionsContainer = document.getElementById('conditions-container');
        const sqlOutput = document.getElementById('sql-output');

        // --- Modal Logic ---
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            modal.classList.remove('hidden');
            setTimeout(() => {
                modal.querySelector('.modal-content').classList.remove('scale-95');
                modal.classList.remove('opacity-0');
            }, 10);
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            modal.querySelector('.modal-content').classList.add('scale-95');
            modal.classList.add('opacity-0');
            setTimeout(() => modal.classList.add('hidden'), 300);
        }

        // --- Template & Conditions Logic ---
        async function fetchSqlTemplates() {
            try {
                const response = await fetch('/api/sql-templates');
                if (!response.ok) {
                    throw new Error('Failed to fetch SQL templates');
                }
                sqlTemplates = await response.json();
                populateTemplateOptions();
            } catch (error) {
                console.error(error);
                ruleTemplateSelect.innerHTML = '<option value="">无法加载模板</option>';
            }
        }

        function populateTemplateOptions() {
            ruleTemplateSelect.innerHTML = '<option value="">选择一个模板</option>';
            for (const key in sqlTemplates) {
                const option = document.createElement('option');
                option.value = key;
                option.textContent = key;
                ruleTemplateSelect.appendChild(option);
            }
        }

        // 获取模板推荐
        async function getTemplateRecommendations() {
            const ruleName = document.getElementById('rule-name').value.trim();
            const ruleType = document.getElementById('rule-type').value;
            const patientType = document.getElementById('patient-type').value;
            const matchMethod = document.getElementById('match-method').value;
            const databaseType = document.getElementById('database-type').value;

            if (!ruleName && !ruleType) {
                alert('请先输入规则名称或选择规则类型！');
                return;
            }

            try {
                // 构建规则对象
                const rule = {
                    name: ruleName,
                    类型: ruleType,
                    适用范围: patientType,
                    匹配方式: matchMethod
                };

                // 调用模板选择API
                const response = await fetch('/api/templates/select', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        rule: rule,
                        db_type: databaseType === 'oracle' ? 'oracle' : 'pg',
                        code_type: matchMethod === 'code' ? 'code' : 'name',
                        patient_type: patientType === 'outpatient' ? 'outpatient' : 'inpatient'
                    }),
                });

                const result = await response.json();
                
                if (result.success && result.template) {
                    // 显示推荐的模板
                    displayTemplateRecommendation(result.template);
                    
                    // 自动选择推荐的模板
                    document.getElementById('rule-template').value = result.template.name;
                    setSqlContent(result.template.content);
                    parseAndRenderConditions(result.template.content);
                } else {
                    document.getElementById('template-recommendations').innerHTML = 
                        '<p class="text-yellow-500 text-sm">未找到匹配的模板推荐</p>';
                }
            } catch (error) {
                console.error('Error getting template recommendations:', error);
                document.getElementById('template-recommendations').innerHTML = 
                    '<p class="text-red-500 text-sm">获取模板推荐失败: ' + error.message + '</p>';
            }
        }

        // 显示模板推荐
        function displayTemplateRecommendation(template) {
            const recommendationsDiv = document.getElementById('template-recommendations');
            recommendationsDiv.innerHTML = `
                <div class="bg-green-900 border border-green-600 rounded-lg p-3">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="text-green-300 font-semibold">推荐模板</h4>
                        <span class="text-xs text-green-400">${template.folder}</span>
                    </div>
                    <p class="text-green-200 text-sm mb-2">${template.name}</p>
                    <button onclick="useRecommendedTemplate('${template.name}')" 
                            class="bg-green-600 hover:bg-green-700 text-white text-xs px-3 py-1 rounded">
                        使用此模板
                    </button>
                </div>
            `;
        }

        // 使用推荐的模板
        function useRecommendedTemplate(templateName) {
            document.getElementById('rule-template').value = templateName;
            const sqlContent = sqlTemplates[templateName] || '';
            setSqlContent(sqlContent);
            parseAndRenderConditions(sqlContent);
        }

        // 手动选择模板时的动态显示
        async function onTemplateSelectionChange() {
            const templateName = document.getElementById('rule-template').value;
            const databaseType = document.getElementById('database-type').value;
            const matchMethod = document.getElementById('match-method').value;
            const patientType = document.getElementById('patient-type').value;

            if (!templateName) {
                setSqlContent('-- 在左侧表单中填写信息以生成SQL...');
                parseAndRenderConditions('');
                return;
            }

            try {
                // 尝试从特定文件夹获取模板
                const response = await fetch('/api/templates/manual-select', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        template_name: templateName,
                        folder_name: `rule_${databaseType === 'oracle' ? 'oracle' : 'pg'}_${matchMethod === 'code' ? 'code' : 'name'}_${patientType === 'outpatient' ? 'outpatient' : 'inpatient'}`
                    }),
                });

                const result = await response.json();
                
                if (result.success && result.template) {
                    setSqlContent(result.template.content);
                    parseAndRenderConditions(result.template.content);
                    
                    // 显示模板信息
                    showTemplateInfo(result.template);
                } else {
                    // 回退到原有逻辑
                    const sqlContent = sqlTemplates[templateName] || '-- 模板未找到';
                    setSqlContent(sqlContent);
                    parseAndRenderConditions(sqlContent);
                }
            } catch (error) {
                console.error('Error selecting template:', error);
                // 回退到原有逻辑
                const sqlContent = sqlTemplates[templateName] || '-- 模板加载失败';
                setSqlContent(sqlContent);
                parseAndRenderConditions(sqlContent);
            }
        }

        // 显示模板信息
        function showTemplateInfo(template) {
            const recommendationsDiv = document.getElementById('template-recommendations');
            recommendationsDiv.innerHTML = `
                <div class="bg-blue-900 border border-blue-600 rounded-lg p-3">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="text-blue-300 font-semibold">当前模板</h4>
                        <span class="text-xs text-blue-400">${template.folder}</span>
                    </div>
                    <p class="text-blue-200 text-sm">${template.name}</p>
                </div>
            `;
        }

        ruleTemplateSelect.addEventListener('change', onTemplateSelectionChange);

        function parseAndRenderConditions(sql) {
            conditionsContainer.innerHTML = '';
            const regex = /\{\{(.*?)\}\}/g;
            let match;
            const foundConditions = new Set();

            while ((match = regex.exec(sql)) !== null) {
                const paramName = match[1].trim();
                if (foundConditions.has(paramName)) continue;
                foundConditions.add(paramName);

                const isSearchable = paramName.includes('编码') || paramName.includes('名称');

                const conditionHtml = `
                    <div class="flex items-center space-x-2" data-param-name="${paramName}">
                        <label class="w-1/4 text-sm">${paramName}</label>
                        <select class="condition-op bg-gray-700 border border-gray-600 rounded-lg p-2.5 text-sm w-1/4">
                            <option>等于</option><option>不等于</option><option>包含</option><option>IN</option>
                        </select>
                        <div class="relative w-1/2">
                            <input type="text" class="condition-value bg-gray-700 border border-gray-600 rounded-lg p-2.5 text-sm w-full pr-10">
                            ${isSearchable ? `<button onclick="openModal('search-modal')" class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-white"><i class="fas fa-search"></i></button>` : ''}
                        </div>
                    </div>
                `;
                conditionsContainer.insertAdjacentHTML('beforeend', conditionHtml);
            }
            if (foundConditions.size === 0) {
                conditionsContainer.innerHTML = '<p class="text-gray-500">该模板没有可配置的条件。</p>';
            }
        }

        // --- SQL Editor Mode Management ---
        function toggleEditMode() {
            const sqlPreview = document.getElementById('sql-preview');
            const sqlEditor = document.getElementById('sql-editor');
            const sqlOutput = document.getElementById('sql-output');
            const editModeText = document.getElementById('edit-mode-text');
            const toggleButton = document.getElementById('toggle-edit-mode');

            if (isEditMode) {
                // 切换到预览模式
                const editorContent = sqlEditor.value;
                sqlOutput.textContent = editorContent;
                hljs.highlightElement(sqlOutput);

                sqlPreview.style.display = 'block';
                sqlEditor.style.display = 'none';

                editModeText.textContent = '编辑模式';
                toggleButton.innerHTML = '<i class="fas fa-edit mr-2"></i> <span id="edit-mode-text">编辑模式</span>';
                isEditMode = false;
            } else {
                // 切换到编辑模式
                const previewContent = sqlOutput.textContent;
                sqlEditor.value = previewContent;

                sqlPreview.style.display = 'none';
                sqlEditor.style.display = 'block';

                editModeText.textContent = '预览模式';
                toggleButton.innerHTML = '<i class="fas fa-eye mr-2"></i> <span id="edit-mode-text">预览模式</span>';
                isEditMode = true;
                sqlEditor.focus();
            }
        }

        function getCurrentSqlContent() {
            if (isEditMode) {
                return document.getElementById('sql-editor').value;
            } else {
                return document.getElementById('sql-output').textContent;
            }
        }

        function setSqlContent(content) {
            const sqlOutput = document.getElementById('sql-output');
            const sqlEditor = document.getElementById('sql-editor');
            
            sqlOutput.textContent = content;
            sqlEditor.value = content;
            hljs.highlightElement(sqlOutput);
        }

        // --- Rule Attribute Management Logic ---
        async function loadRuleAttributeOptions() {
            try {
                const response = await fetch('/api/rules/attribute-options');
                if (!response.ok) {
                    throw new Error('Failed to fetch attribute options');
                }
                const data = await response.json();
                
                // Populate rule type options
                const ruleTypeSelect = document.getElementById('rule-type');
                data.rule_types.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type;
                    option.textContent = type;
                    ruleTypeSelect.appendChild(option);
                });

            } catch (error) {
                console.error('Error loading attribute options:', error);
            }
        }

        async function analyzeRuleName() {
            const ruleName = document.getElementById('rule-name').value.trim();
            
            if (!ruleName) {
                alert('请先输入规则名称！');
                return;
            }

            try {
                const response = await fetch('/api/intelligent-template/analyze-rule-name', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        rule_name: ruleName
                    }),
                });

                if (!response.ok) {
                    throw new Error('Failed to analyze rule name');
                }

                const analysis = await response.json();
                
                // Update form fields with analysis results
                if (analysis.detected_rule_type) {
                    document.getElementById('rule-type').value = analysis.detected_rule_type;
                }
                if (analysis.detected_patient_type) {
                    document.getElementById('patient-type').value = analysis.detected_patient_type;
                }
                if (analysis.detected_match_method) {
                    document.getElementById('match-method').value = analysis.detected_match_method;
                }
                if (analysis.suggested_database_type) {
                    document.getElementById('database-type').value = analysis.suggested_database_type;
                }

                // Show analysis results
                const confidence = Math.round(analysis.confidence * 100);
                alert(`智能分析完成！\n检测置信度: ${confidence}%\n\n检测结果:\n` +
                      `规则类型: ${analysis.detected_rule_type || '未检测到'}\n` +
                      `适用范围: ${translatePatientType(analysis.detected_patient_type) || '未检测到'}\n` +
                      `匹配方式: ${translateMatchMethod(analysis.detected_match_method) || '未检测到'}\n` +
                      `建议数据库: ${analysis.suggested_database_type || 'PostgreSQL'}`);

            } catch (error) {
                console.error('Error analyzing rule name:', error);
                alert('分析规则名称时出错: ' + error.message);
            }
        }

        async function getTemplateRecommendations() {
            const ruleName = document.getElementById('rule-name').value.trim();
            const ruleType = document.getElementById('rule-type').value;
            const patientType = document.getElementById('patient-type').value;
            const matchMethod = document.getElementById('match-method').value;
            const databaseType = document.getElementById('database-type').value;

            if (!ruleName) {
                alert('请先输入规则名称！');
                return;
            }

            try {
                const response = await fetch('/api/intelligent-template/recommend', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        rule_name: ruleName,
                        rule_attributes: {
                            rule_type: ruleType,
                            patient_type: patientType,
                            match_method: matchMethod,
                            database_type: databaseType
                        }
                    }),
                });

                if (!response.ok) {
                    throw new Error('Failed to get template recommendations');
                }

                const data = await response.json();
                const recommendations = data.recommendations || [];
                
                displayTemplateRecommendations(recommendations);

            } catch (error) {
                console.error('Error getting template recommendations:', error);
                alert('获取模板推荐时出错: ' + error.message);
            }
        }

        function displayTemplateRecommendations(recommendations) {
            const container = document.getElementById('template-recommendations');
            
            if (recommendations.length === 0) {
                container.innerHTML = '<p class="text-gray-500 text-sm">没有找到匹配的模板推荐</p>';
                return;
            }

            let html = '<div class="space-y-2">';
            html += '<p class="text-sm text-gray-300 mb-3">推荐模板 (按匹配度排序):</p>';
            
            recommendations.forEach((rec, index) => {
                const confidence = Math.round(rec.confidence * 100);
                html += `
                    <div class="bg-gray-700 p-3 rounded-lg border border-gray-600">
                        <div class="flex justify-between items-center">
                            <div>
                                <span class="text-white font-medium">${rec.template_id}</span>
                                <span class="text-green-400 text-sm ml-2">${confidence}% 匹配</span>
                            </div>
                            <button onclick="selectRecommendedTemplate('${rec.template_id}')" 
                                class="bg-purple-600 hover:bg-purple-700 text-white text-xs px-3 py-1 rounded">
                                选择
                            </button>
                        </div>
                        <p class="text-gray-300 text-sm mt-1">${rec.reason}</p>
                    </div>
                `;
            });
            html += '</div>';
            
            container.innerHTML = html;
        }

        function selectRecommendedTemplate(templateId) {
            // Set the template in the dropdown
            document.getElementById('rule-template').value = templateId;
            
            // Trigger the change event to load the template
            const event = new Event('change');
            document.getElementById('rule-template').dispatchEvent(event);
            
            // Show success message
            const templateName = document.getElementById('rule-template').selectedOptions[0].text;
            alert(`已选择推荐模板: ${templateName}`);
        }

        function translatePatientType(patientType) {
            const translations = {
                'inpatient': '住院',
                'outpatient': '门诊',
                'general': '通用'
            };
            return translations[patientType] || patientType;
        }

        function translateMatchMethod(matchMethod) {
            const translations = {
                'name': '按名称',
                'code': '按编码'
            };
            return translations[matchMethod] || matchMethod;
        }

        function validateRuleAttributes() {
            const ruleType = document.getElementById('rule-type').value;
            const patientType = document.getElementById('patient-type').value;
            const matchMethod = document.getElementById('match-method').value;
            const databaseType = document.getElementById('database-type').value;
            
            const errors = [];
            
            // Add validation rules as needed
            if (!databaseType) {
                errors.push('请选择数据库类型');
            }
            
            return errors;
        }

        // --- Edit and Delete Logic ---
        async function editRule(ruleName) {
            try {
                const response = await fetch(`/api/rules/${ruleName}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const rule = await response.json();

                // 设置编辑状态
                isEditingExistingRule = true;
                originalRuleName = rule.name;

                // 填充规则名称
                document.getElementById('rule-name').value = rule.name;

                // 填充规则属性
                if (rule.rule_type) {
                    document.getElementById('rule-type').value = rule.rule_type;
                }
                if (rule.patient_type) {
                    document.getElementById('patient-type').value = rule.patient_type;
                }
                if (rule.match_method) {
                    document.getElementById('match-method').value = rule.match_method;
                }
                if (rule.database_type) {
                    document.getElementById('database-type').value = rule.database_type;
                }
                if (rule.template_id) {
                    document.getElementById('rule-template').value = rule.template_id;
                }

                // 设置SQL内容
                setSqlContent(rule.content);

                // 清空模板推荐
                document.getElementById('template-recommendations').innerHTML = '<p class="text-gray-500 text-sm">点击"获取推荐"按钮获取智能模板推荐</p>';
                document.getElementById('conditions-container').innerHTML = '<p class="text-gray-500">当前为编辑模式，可直接修改SQL内容</p>';

                // 自动切换到编辑模式以便用户修改
                if (!isEditMode) {
                    toggleEditMode();
                }

                // 更新保存按钮文本
                const saveButton = document.getElementById('save-rule-btn');
                saveButton.innerHTML = '<i class="fas fa-save mr-2"></i> 更新规则';

                // 滚动到编辑器顶部
                window.scrollTo({ top: 0, behavior: 'smooth' });

                alert(`已加载规则 "${rule.name}" 进行编辑。您可以直接在编辑器中修改SQL内容。`);
            } catch (error) {
                console.error('Error fetching rule for editing:', error);
                alert('加载规则进行编辑时出错: ' + error.message);
            }
        }

        function resetEditingState() {
            isEditingExistingRule = false;
            originalRuleName = null;

            // 恢复保存按钮文本
            const saveButton = document.getElementById('save-rule-btn');
            saveButton.innerHTML = '<i class="fas fa-check-circle mr-2"></i> 保存规则';
        }

        async function deleteRule(ruleName) {
            if (!confirm(`您确定要删除规则 "${ruleName}" 吗？此操作无法撤销。`)) {
                return;
            }

            try {
                const response = await fetch(`/api/rules/${ruleName}`, {
                    method: 'DELETE',
                });
                const result = await response.json();
                if (response.ok) {
                    alert(result.message);
                    await loadSavedRules(); // Refresh the list
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                console.error('Error deleting rule:', error);
                alert(`删除规则时出错: ${error.message}`);
            }
        }

        // --- Save Rule Logic ---
        async function saveRule() {
            const ruleName = document.getElementById('rule-name').value.trim();
            const sqlContent = getCurrentSqlContent();

            if (!ruleName) {
                alert('请输入规则名称！');
                return;
            }

            if (!sqlContent || sqlContent.trim() === '' || sqlContent.startsWith('-- 在左侧表单中填写信息以生成SQL...')) {
                alert('没有可保存的SQL内容！请先选择模板并填写条件，或直接在编辑模式下编写SQL。');
                return;
            }

            // Validate rule attributes
            const attributeErrors = validateRuleAttributes();
            if (attributeErrors.length > 0) {
                alert('规则属性验证失败:\n' + attributeErrors.join('\n'));
                return;
            }

            // 确保内容是字符串类型
            const cleanSqlContent = String(sqlContent).trim();

            // Collect rule attributes
            const ruleAttributes = {
                name: ruleName,
                content: cleanSqlContent,
                description: `通过规则编辑器${isEditingExistingRule ? '更新' : '创建'}的规则`,
                category: 'manual',
                rule_type: document.getElementById('rule-type').value || null,
                patient_type: document.getElementById('patient-type').value || null,
                match_method: document.getElementById('match-method').value || null,
                database_type: document.getElementById('database-type').value || 'postgresql',
                template_id: document.getElementById('rule-template').value || null
            };

            console.log('准备保存规则:', {
                ...ruleAttributes,
                isEditing: isEditingExistingRule,
                originalName: originalRuleName
            });

            try {
                let response;

                if (isEditingExistingRule && originalRuleName) {
                    // 更新现有规则
                    response = await fetch(`/api/rules/${originalRuleName}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(ruleAttributes),
                    });
                } else {
                    // 创建新规则
                    response = await fetch('/api/rules', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(ruleAttributes),
                    });
                }

                const result = await response.json();

                if (response.ok) {
                    const action = isEditingExistingRule ? '更新' : '创建';
                    alert(`成功: 规则 "${ruleName}" ${action}成功！`);

                    // 清空表单和重置状态
                    document.getElementById('rule-name').value = '';
                    document.getElementById('rule-template').value = '';
                    setSqlContent('-- 在左侧表单中填写信息以生成SQL...');
                    document.getElementById('conditions-container').innerHTML = '<p class="text-gray-500">请先选择一个SQL模板</p>';

                    // 重置编辑状态
                    resetEditingState();

                    // 如果在编辑模式，切换回预览模式
                    if (isEditMode) {
                        toggleEditMode();
                    }

                    // 刷新规则列表
                    await loadSavedRules();
                } else {
                    alert(`错误: ${result.message || '保存失败'}`);
                }
            } catch (error) {
                console.error('Error saving rule:', error);
                alert('保存规则时发生网络错误: ' + error.message);
            }
        }

        // --- Rule Management Logic ---
        async function loadSavedRules() {
            try {
                const response = await fetch('/api/rules');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                const rules = data.rules || [];
                const tableBody = document.getElementById('rules-table-body');
                
                if (rules.length === 0) {
                    tableBody.innerHTML = '<tr><td colspan="5" class="text-center py-4 text-gray-500">暂无规则</td></tr>';
                    return;
                }

                tableBody.innerHTML = '';
                rules.forEach(rule => {
                    const createdAt = rule.created_at ? new Date(rule.created_at).toLocaleDateString('zh-CN') : '未知';
                    const categoryText = rule.category === 'manual' ? '手动创建' : 
                                       rule.category === 'template' ? '模板生成' : 
                                       rule.category === 'intelligent' ? '智能生成' : '其他';
                    
                    const statusClass = rule.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800';
                    const statusText = rule.status === 'active' ? '活跃' : '非活跃';
                    
                    const row = `
                        <tr class="bg-gray-800 border-b border-gray-700 hover:bg-gray-700">
                            <td class="px-6 py-4 font-medium text-white whitespace-nowrap" title="${rule.description || ''}">${rule.name}</td>
                            <td class="px-6 py-4">${categoryText}</td>
                            <td class="px-6 py-4"><span class="${statusClass} text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full">${statusText}</span></td>
                            <td class="px-6 py-4">${createdAt}</td>
                            <td class="px-6 py-4 text-center">
                                <button onclick="editRule('${rule.name}')" class="font-medium text-blue-500 hover:underline">编辑</button>
                                <button onclick="deleteRule('${rule.name}')" class="font-medium text-red-500 hover:underline ml-4">删除</button>
                            </td>
                        </tr>
                    `;
                    tableBody.innerHTML += row;
                });

                // 显示分页信息（如果有的话）
                if (data.pagination) {
                    console.log(`加载了 ${rules.length} 个规则，总共 ${data.pagination.total_count} 个`);
                }

            } catch (error) {
                console.error('Error fetching saved rules:', error);
                const tableBody = document.getElementById('rules-table-body');
                tableBody.innerHTML = '<tr><td colspan="5" class="text-center py-4 text-red-400">加载规则失败: ' + error.message + '</td></tr>';
            }
        }

        // --- Workflow Logic ---
        function initializeWorkflowWizard() {
            currentWorkflowStep = 0;
            updateWorkflowProgress();
            loadWorkflowStep(workflowSteps[currentWorkflowStep]);
        }

        function closeWorkflowWizard() {
            closeModal('workflow-wizard-modal');
            currentWorkflow = null;
            currentWorkflowStep = 0;
        }

        function workflowPrevStep() {
            if (currentWorkflowStep > 0) {
                currentWorkflowStep--;
                updateWorkflowProgress();
                loadWorkflowStep(workflowSteps[currentWorkflowStep]);
            }
        }

        function workflowNextStep() {
            if (currentWorkflowStep < workflowSteps.length - 1) {
                currentWorkflowStep++;
                updateWorkflowProgress();
                loadWorkflowStep(workflowSteps[currentWorkflowStep]);
            } else {
                // 完成向导
                completeWorkflowWizard();
            }
        }

        function updateWorkflowProgress() {
            const progress = ((currentWorkflowStep + 1) / workflowSteps.length) * 100;
            document.getElementById('workflow-progress-bar').style.width = progress + '%';
            document.getElementById('workflow-progress-text').textContent = `${currentWorkflowStep + 1}/${workflowSteps.length}`;
            
            // 更新按钮状态
            document.getElementById('workflow-prev-btn').disabled = currentWorkflowStep === 0;
            const nextBtn = document.getElementById('workflow-next-btn');
            if (currentWorkflowStep === workflowSteps.length - 1) {
                nextBtn.innerHTML = '<i class="fas fa-check mr-2"></i> 完成';
            } else {
                nextBtn.innerHTML = '下一步 <i class="fas fa-arrow-right ml-2"></i>';
            }
        }

        function loadWorkflowStep(stepName) {
            const contentDiv = document.getElementById('workflow-content');
            const summaryDiv = document.getElementById('workflow-summary');
            
            switch(stepName) {
                case 'rule_attributes':
                    contentDiv.innerHTML = `
                        <h3 class="text-xl font-semibold text-white mb-4">第1步: 规则属性设置</h3>
                        <div class="space-y-4">
                            <div>
                                <label class="block mb-2 text-sm font-medium text-gray-300">规则名称</label>
                                <input type="text" id="wizard-rule-name" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" placeholder="例如：限定支付-限单人多次">
                            </div>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block mb-2 text-sm font-medium text-gray-300">规则类型</label>
                                    <select id="wizard-rule-type" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                        <option value="">请选择</option>
                                        <option value="重复收费">重复收费</option>
                                        <option value="超住院天数">超住院天数</option>
                                        <option value="限定支付">限定支付</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block mb-2 text-sm font-medium text-gray-300">适用范围</label>
                                    <select id="wizard-patient-type" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                        <option value="inpatient">住院</option>
                                        <option value="outpatient">门诊</option>
                                        <option value="general">通用</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block mb-2 text-sm font-medium text-gray-300">匹配方式</label>
                                    <select id="wizard-match-method" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                        <option value="name">按名称</option>
                                        <option value="code">按编码</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block mb-2 text-sm font-medium text-gray-300">数据库类型</label>
                                    <select id="wizard-database-type" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                        <option value="postgresql">PostgreSQL</option>
                                        <option value="oracle">Oracle</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    `;
                    break;
                case 'template_selection':
                    contentDiv.innerHTML = `
                        <h3 class="text-xl font-semibold text-white mb-4">第2步: 模板选择</h3>
                        <div class="space-y-4">
                            <div id="wizard-template-recommendations" class="space-y-2">
                                <p class="text-gray-500 text-sm">正在获取推荐模板...</p>
                            </div>
                        </div>
                    `;
                    // 自动获取模板推荐
                    getWizardTemplateRecommendations();
                    break;
                case 'parameter_input':
                    contentDiv.innerHTML = `
                        <h3 class="text-xl font-semibold text-white mb-4">第3步: 参数填写</h3>
                        <div id="wizard-conditions-container" class="space-y-4">
                            <p class="text-gray-500">请先完成模板选择</p>
                        </div>
                    `;
                    break;
                case 'sql_preview':
                    contentDiv.innerHTML = `
                        <h3 class="text-xl font-semibold text-white mb-4">第4步: SQL预览</h3>
                        <div class="bg-gray-900 rounded-lg p-4">
                            <pre id="wizard-sql-preview" class="language-sql"><code class="language-sql">-- SQL将在此处显示</code></pre>
                        </div>
                    `;
                    break;
                case 'rule_creation':
                    contentDiv.innerHTML = `
                        <h3 class="text-xl font-semibold text-white mb-4">第5步: 规则创建</h3>
                        <div class="space-y-4">
                            <div class="bg-green-900 border border-green-600 rounded-lg p-4">
                                <h4 class="text-green-300 font-semibold mb-2">规则创建完成</h4>
                                <p class="text-green-200 text-sm">您的规则已成功创建并保存。</p>
                            </div>
                            <button onclick="applyWizardToMainForm()" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg">
                                应用到主表单
                            </button>
                        </div>
                    `;
                    break;
            }
            
            updateWorkflowSummary();
        }

        function updateWorkflowSummary() {
            const summaryDiv = document.getElementById('workflow-summary');
            let summaryHtml = '<div class="space-y-2 text-sm">';
            
            // 根据当前步骤显示摘要信息
            if (currentWorkflowStep >= 0) {
                const ruleName = document.getElementById('wizard-rule-name')?.value || '未设置';
                const ruleType = document.getElementById('wizard-rule-type')?.value || '未设置';
                summaryHtml += `
                    <div><span class="text-gray-400">规则名称:</span> <span class="text-white">${ruleName}</span></div>
                    <div><span class="text-gray-400">规则类型:</span> <span class="text-white">${ruleType}</span></div>
                `;
            }
            
            summaryHtml += '</div>';
            summaryDiv.innerHTML = summaryHtml;
        }

        async function getWizardTemplateRecommendations() {
            const ruleName = document.getElementById('wizard-rule-name')?.value || '';
            const ruleType = document.getElementById('wizard-rule-type')?.value || '';
            const patientType = document.getElementById('wizard-patient-type')?.value || 'inpatient';
            const matchMethod = document.getElementById('wizard-match-method')?.value || 'name';
            const databaseType = document.getElementById('wizard-database-type')?.value || 'postgresql';

            try {
                const rule = {
                    name: ruleName,
                    类型: ruleType,
                    适用范围: patientType,
                    匹配方式: matchMethod
                };

                const response = await fetch('/api/templates/select', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        rule: rule,
                        db_type: databaseType === 'oracle' ? 'oracle' : 'pg',
                        code_type: matchMethod === 'code' ? 'code' : 'name',
                        patient_type: patientType === 'outpatient' ? 'outpatient' : 'inpatient'
                    }),
                });

                const result = await response.json();
                
                if (result.success && result.template) {
                    document.getElementById('wizard-template-recommendations').innerHTML = `
                        <div class="bg-green-900 border border-green-600 rounded-lg p-4">
                            <h4 class="text-green-300 font-semibold mb-2">推荐模板</h4>
                            <p class="text-green-200 text-sm mb-2">${result.template.name}</p>
                            <p class="text-green-400 text-xs">${result.template.folder}</p>
                        </div>
                    `;
                } else {
                    document.getElementById('wizard-template-recommendations').innerHTML = 
                        '<p class="text-yellow-500 text-sm">未找到匹配的模板推荐</p>';
                }
            } catch (error) {
                console.error('Error getting wizard template recommendations:', error);
                document.getElementById('wizard-template-recommendations').innerHTML = 
                    '<p class="text-red-500 text-sm">获取模板推荐失败</p>';
            }
        }

        function completeWorkflowWizard() {
            // 完成向导逻辑
            alert('智能规则创建向导完成！');
            closeWorkflowWizard();
        }

        function applyWizardToMainForm() {
            // 将向导中的设置应用到主表单
            const wizardData = {
                ruleName: document.getElementById('wizard-rule-name')?.value || '',
                ruleType: document.getElementById('wizard-rule-type')?.value || '',
                patientType: document.getElementById('wizard-patient-type')?.value || '',
                matchMethod: document.getElementById('wizard-match-method')?.value || '',
                databaseType: document.getElementById('wizard-database-type')?.value || ''
            };
            
            // 应用到主表单
            document.getElementById('rule-name').value = wizardData.ruleName;
            document.getElementById('rule-type').value = wizardData.ruleType;
            document.getElementById('patient-type').value = wizardData.patientType;
            document.getElementById('match-method').value = wizardData.matchMethod;
            document.getElementById('database-type').value = wizardData.databaseType;
            
            closeWorkflowWizard();
            alert('向导设置已应用到主表单！');
        }

        // --- Event Listeners ---
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize the application
            fetchSqlTemplates();
            loadSavedRules();
            loadRuleAttributeOptions();

            // Bind event listeners
            document.getElementById('toggle-edit-mode').addEventListener('click', toggleEditMode);
            document.getElementById('save-rule-btn').addEventListener('click', saveRule);
            document.getElementById('analyze-rule-btn').addEventListener('click', analyzeRuleName);
            document.getElementById('get-recommendations-btn').addEventListener('click', getTemplateRecommendations);
            document.getElementById('start-wizard-btn').addEventListener('click', function() {
                openModal('workflow-wizard-modal');
                initializeWorkflowWizard();
            });
            
            // 添加属性变化时的模板更新监听器
            document.getElementById('database-type').addEventListener('change', onTemplateSelectionChange);
            document.getElementById('match-method').addEventListener('change', onTemplateSelectionChange);
            document.getElementById('patient-type').addEventListener('change', onTemplateSelectionChange);
            
            // New rule button
            document.getElementById('new-rule-btn').addEventListener('click', function() {
                // 清空所有表单字段
                document.getElementById('rule-name').value = '';
                document.getElementById('rule-type').value = '';
                document.getElementById('patient-type').value = '';
                document.getElementById('match-method').value = '';
                document.getElementById('database-type').value = 'postgresql';
                document.getElementById('rule-template').value = '';
                
                // 重置SQL内容
                setSqlContent('-- 在左侧表单中填写信息以生成SQL...');
                
                // 清空条件容器和推荐
                document.getElementById('conditions-container').innerHTML = '<p class="text-gray-500">请先选择一个SQL模板</p>';
                document.getElementById('template-recommendations').innerHTML = '<p class="text-gray-500 text-sm">点击"获取推荐"按钮获取智能模板推荐</p>';
                
                // 重置编辑状态
                resetEditingState();
                
                // 如果在编辑模式，切换回预览模式
                if (isEditMode) {
                    toggleEditMode();
                }

                alert('已清空表单，可以开始创建新规则。');
            });

            // Copy SQL button
            document.getElementById('copy-sql').addEventListener('click', function() {
                const sqlContent = getCurrentSqlContent();
                navigator.clipboard.writeText(sqlContent).then(() => {
                    // 显示复制成功提示
                    const copyButton = document.getElementById('copy-sql');
                    const originalText = copyButton.innerHTML;
                    copyButton.innerHTML = '<i class="fas fa-check mr-2"></i> 已复制';
                    setTimeout(() => {
                        copyButton.innerHTML = originalText;
                    }, 2000);
                });
            });
        });

    </script>
</body>

</html>