<!DOCTYPE html>
<html lang="en" class="dark">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>临时规则编写工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/atom-one-dark.min.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'gray-900': '#121212',
                        'gray-800': '#1e1e1e',
                        'gray-700': '#2d2d2d',
                        'gray-600': '#444444',
                    }
                }
            }
        }
    </script>
    <style>
        /* For modal transitions */
        .modal-backdrop {
            transition: opacity 0.3s ease;
        }

        .modal-content {
            transition: transform 0.3s ease;
        }
    </style>
</head>

<body class="bg-gray-900 text-gray-300 font-sans p-8">

    <!-- Main Container -->
    <div class="container mx-auto space-y-8">

        <!-- Part 1: SQL Generator -->
        <div class="bg-gray-800 p-6 rounded-lg shadow-lg">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-bold text-white"><i class="fas fa-magic-wand-sparkles mr-2"></i>规则SQL生成器</h1>
                <div class="flex space-x-2">
                    <button id="start-wizard-btn"
                        class="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded-lg flex items-center">
                        <i class="fas fa-magic mr-2"></i> 智能向导
                    </button>
                    <button onclick="openModal('db-config-modal')"
                        class="bg-gray-700 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg flex items-center">
                        <i class="fas fa-database mr-2"></i> 数据库参数配置
                    </button>
                </div>
            </div>

            <div class="flex space-x-8">
                <!-- Left Panel: Form -->
                <div class="w-1/2 space-y-6">
                    <div>
                        <label for="rule-name" class="block mb-2 text-sm font-medium text-gray-300">规则名称</label>
                        <input type="text" id="rule-name"
                            class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                            placeholder="例如：限定支付-限单人多次">
                    </div>

                    <!-- Rule Attributes Section -->
                    <div class="border border-gray-600 rounded-lg p-4 mb-4">
                        <h3 class="text-lg font-semibold text-white mb-4">规则属性</h3>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label for="rule-type" class="block mb-2 text-sm font-medium text-gray-300">规则类型</label>
                                <select id="rule-type"
                                    class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                    <option value="">自动检测</option>
                                </select>
                            </div>
                            <div>
                                <label for="patient-type" class="block mb-2 text-sm font-medium text-gray-300">适用范围</label>
                                <select id="patient-type"
                                    class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                    <option value="">自动检测</option>
                                    <option value="inpatient">住院</option>
                                    <option value="outpatient">门诊</option>
                                    <option value="general">通用</option>
                                </select>
                            </div>
                            <div>
                                <label for="match-method" class="block mb-2 text-sm font-medium text-gray-300">匹配方式</label>
                                <select id="match-method"
                                    class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                    <option value="">自动检测</option>
                                    <option value="name">按名称</option>
                                    <option value="code">按编码</option>
                                </select>
                            </div>
                            <div>
                                <label for="database-type" class="block mb-2 text-sm font-medium text-gray-300">数据库类型</label>
                                <select id="database-type"
                                    class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                    <option value="postgresql">PostgreSQL</option>
                                    <option value="oracle">Oracle</option>
                                </select>
                            </div>
                        </div>
                        <div class="mt-4 flex space-x-2">
                            <button id="analyze-rule-btn"
                                class="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded-lg flex items-center text-sm">
                                <i class="fas fa-magic mr-2"></i> 智能分析
                            </button>
                            <button id="get-recommendations-btn"
                                class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg flex items-center text-sm">
                                <i class="fas fa-lightbulb mr-2"></i> 获取推荐
                            </button>
                        </div>
                    </div>

                    <!-- Template Selection Section -->
                    <div class="border border-gray-600 rounded-lg p-4 mb-4">
                        <h3 class="text-lg font-semibold text-white mb-4">模板选择</h3>
                        <div id="template-recommendations" class="space-y-2 mb-4">
                            <p class="text-gray-500 text-sm">点击"获取推荐"按钮获取智能模板推荐</p>
                        </div>
                        <div>
                            <label for="rule-template" class="block mb-2 text-sm font-medium text-gray-300">或手动选择模板</label>
                            <select id="rule-template"
                                class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                <option value="">选择一个模板</option>
                            </select>
                        </div>
                    </div>

                    <div class="border-t border-gray-700 pt-6">
                        <h3 class="text-lg font-semibold text-white mb-4">规则条件 (由模板生成)</h3>
                        <div class="space-y-4" id="conditions-container">
                            <p class="text-gray-500">请先选择一个SQL模板</p>
                        </div>
                    </div>
                </div>

                <!-- Right Panel: SQL Editor -->
                <div class="w-1/2 flex flex-col">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-semibold text-white">SQL编辑器</h2>
                        <div class="flex space-x-2">
                            <button id="toggle-edit-mode"
                                class="bg-gray-700 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg flex items-center text-sm">
                                <i class="fas fa-edit mr-2"></i> <span id="edit-mode-text">编辑模式</span>
                            </button>
                            <button id="copy-sql"
                                class="bg-gray-700 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg flex items-center text-sm">
                                <i class="fas fa-copy mr-2"></i> 复制
                            </button>
                        </div>
                    </div>
                    <div class="bg-gray-900 rounded-lg flex-grow p-1 relative">
                        <!-- 预览模式 -->
                        <pre id="sql-preview" class="language-sql h-full block p-4 overflow-auto"
                            style="margin: 0;"><code id="sql-output" class="language-sql">-- 在左侧表单中填写信息以生成SQL...</code></pre>

                        <!-- 编辑模式 -->
                        <textarea id="sql-editor"
                            class="w-full h-full bg-gray-900 text-gray-300 font-mono text-sm p-4 border-none outline-none resize-none"
                            style="display: none; min-height: 300px;"
                            placeholder="-- 在此处编写或编辑SQL代码...">-- 在左侧表单中填写信息以生成SQL...</textarea>
                    </div>
                    <div class="mt-6 flex justify-between">
                        <button id="new-rule-btn"
                            class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-3 px-6 rounded-lg flex items-center">
                            <i class="fas fa-plus mr-2"></i> 新建规则
                        </button>
                        <button id="save-rule-btn"
                            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg flex items-center">
                            <i class="fas fa-check-circle mr-2"></i> 保存规则
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Part 2: Rule Management -->
        <div class="bg-gray-800 p-6 rounded-lg shadow-lg">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-bold text-white"><i class="fas fa-tasks mr-2"></i>规则管理</h1>
            </div>
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left text-gray-400">
                    <thead class="text-xs text-gray-300 uppercase bg-gray-700">
                        <tr>
                            <th scope="col" class="px-6 py-3">规则名称</th>
                            <th scope="col" class="px-6 py-3">来源</th>
                            <th scope="col" class="px-6 py-3">状态</th>
                            <th scope="col" class="px-6 py-3">创建日期</th>
                            <th scope="col" class="px-6 py-3 text-center">操作</th>
                        </tr>
                    </thead>
                    <tbody id="rules-table-body">
                        <!-- Rules will be loaded here dynamically -->
                    </tbody>
                </table>
            </div>
        </div>

    </div>

    <!-- DB Config Modal -->
    <div id="db-config-modal"
        class="modal-backdrop fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center hidden">
        <div class="modal-content bg-gray-800 rounded-lg shadow-xl p-8 w-full max-w-md transform scale-95">
            <h2 class="text-2xl font-bold text-white mb-6">数据库配置</h2>
            <div class="space-y-4">
                <div>
                    <label for="db-type" class="block mb-2 text-sm font-medium text-gray-300">数据库类型</label>
                    <select id="db-type"
                        class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                        <option>PostgreSQL</option>
                        <option>Oracle</option>
                    </select>
                </div>
                <div>
                    <label for="db-host" class="block mb-2 text-sm font-medium text-gray-300">主机</label>
                    <input type="text" id="db-host"
                        class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg block w-full p-2.5"
                        placeholder="localhost">
                </div>
                <div>
                    <label for="db-port" class="block mb-2 text-sm font-medium text-gray-300">端口</label>
                    <input type="text" id="db-port"
                        class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg block w-full p-2.5"
                        placeholder="5432">
                </div>
                <div>
                    <label for="db-user" class="block mb-2 text-sm font-medium text-gray-300">用户名</label>
                    <input type="text" id="db-user"
                        class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg block w-full p-2.5"
                        placeholder="admin">
                </div>
                <div>
                    <label for="db-pass" class="block mb-2 text-sm font-medium text-gray-300">密码</label>
                    <input type="password" id="db-pass"
                        class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg block w-full p-2.5">
                </div>
            </div>
            <div class="flex justify-end space-x-4 mt-8">
                <button onclick="closeModal('db-config-modal')"
                    class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg">取消</button>
                <button class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg">保存</button>
            </div>
        </div>
    </div>

    <!-- Search Modal -->
    <div id="search-modal"
        class="modal-backdrop fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center hidden">
        <div class="modal-content bg-gray-800 rounded-lg shadow-xl p-8 w-full max-w-2xl transform scale-95">
            <h2 class="text-2xl font-bold text-white mb-6">从[医保三目表]检索</h2>
            <input type="text"
                class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg block w-full p-2.5 mb-4"
                placeholder="输入医保编码或名称...">
            <div class="max-h-80 overflow-y-auto">
                <table class="w-full text-sm text-left text-gray-400">
                    <thead class="text-xs text-gray-300 uppercase bg-gray-700 sticky top-0">
                        <tr>
                            <th class="px-4 py-2">医保编码</th>
                            <th class="px-4 py-2">医保名称</th>
                            <th class="px-4 py-2">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="border-b border-gray-700">
                            <td>P001</td>
                            <td>项目一</td>
                            <td><button class="text-blue-500">选择</button></td>
                        </tr>
                        <tr class="border-b border-gray-700">
                            <td>P002</td>
                            <td>项目二</td>
                            <td><button class="text-blue-500">选择</button></td>
                        </tr>
                        <tr class="border-b border-gray-700">
                            <td>D005</td>
                            <td>药品A</td>
                            <td><button class="text-blue-500">选择</button></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="flex justify-end mt-8">
                <button onclick="closeModal('search-modal')"
                    class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg">关闭</button>
            </div>
        </div>
    </div>

    <!-- Intelligent Workflow Wizard Modal -->
    <div id="workflow-wizard-modal"
        class="modal-backdrop fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center hidden">
        <div class="modal-content bg-gray-800 rounded-lg shadow-xl w-full max-w-6xl h-5/6 transform scale-95 flex flex-col">
            <!-- Header -->
            <div class="flex justify-between items-center p-6 border-b border-gray-700">
                <h2 class="text-2xl font-bold text-white">智能规则创建向导</h2>
                <button onclick="closeWorkflowWizard()" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            
            <!-- Progress Bar -->
            <div class="px-6 py-4 border-b border-gray-700">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm text-gray-300">进度</span>
                    <span id="workflow-progress-text" class="text-sm text-gray-300">0/5</span>
                </div>
                <div class="w-full bg-gray-700 rounded-full h-2">
                    <div id="workflow-progress-bar" class="bg-purple-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                </div>
                <div class="flex justify-between mt-2 text-xs text-gray-400">
                    <span>规则属性</span>
                    <span>模板选择</span>
                    <span>参数填写</span>
                    <span>SQL预览</span>
                    <span>规则创建</span>
                </div>
            </div>
            
            <!-- Content Area -->
            <div class="flex-1 flex overflow-hidden">
                <!-- Step Content -->
                <div class="flex-1 p-6 overflow-y-auto">
                    <div id="workflow-content">
                        <!-- Dynamic content will be loaded here -->
                    </div>
                </div>
                
                <!-- Summary Panel -->
                <div class="w-80 bg-gray-900 p-6 border-l border-gray-700 overflow-y-auto">
                    <h3 class="text-lg font-semibold text-white mb-4">规则摘要</h3>
                    <div id="workflow-summary" class="space-y-3 text-sm">
                        <div class="text-gray-400">开始创建规则...</div>
                    </div>
                </div>
            </div>
            
            <!-- Footer -->
            <div class="flex justify-between items-center p-6 border-t border-gray-700">
                <button id="workflow-prev-btn" onclick="workflowPrevStep()" 
                    class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg flex items-center" disabled>
                    <i class="fas fa-arrow-left mr-2"></i> 上一步
                </button>
                <div class="flex space-x-2">
                    <button id="workflow-cancel-btn" onclick="closeWorkflowWizard()" 
                        class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg">
                        取消
                    </button>
                    <button id="workflow-next-btn" onclick="workflowNextStep()" 
                        class="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded-lg flex items-center">
                        下一步 <i class="fas fa-arrow-right ml-2"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- Modal Logic ---
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            modal.classList.remove('hidden');
            setTimeout(() => {
                modal.querySelector('.modal-content').classList.remove('scale-95');
                modal.classList.remove('opacity-0');
            }, 10);
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            modal.querySelector('.modal-content').classList.add('scale-95');
            modal.classList.add('opacity-0');
            setTimeout(() => modal.classList.add('hidden'), 300);
        }

        // --- Template & Conditions Logic ---
        const ruleTemplateSelect = document.getElementById('rule-template');
        const conditionsContainer = document.getElementById('conditions-container');
        const sqlOutput = document.getElementById('sql-output');
        let sqlTemplates = {};

        async function fetchSqlTemplates() {
            try {
                const response = await fetch('/api/sql-templates');
                if (!response.ok) {
                    throw new Error('Failed to fetch SQL templates');
                }
                sqlTemplates = await response.json();
                populateTemplateOptions();
            } catch (error) {
                console.error(error);
                ruleTemplateSelect.innerHTML = '<option value="">无法加载模板</option>';
            }
        }

        function populateTemplateOptions() {
            ruleTemplateSelect.innerHTML = '<option value="">选择一个模板</option>';
            for (const key in sqlTemplates) {
                const option = document.createElement('option');
                option.value = key;
                option.textContent = key;
                ruleTemplateSelect.appendChild(option);
            }
        }

        ruleTemplateSelect.addEventListener('change', function () {
            const templateKey = this.value;
            const sqlContent = sqlTemplates[templateKey] || '-- 在左侧表单中填写信息以生成SQL...';
            setSqlContent(sqlContent);
            parseAndRenderConditions(sqlContent);
        });

        function parseAndRenderConditions(sql) {
            conditionsContainer.innerHTML = '';
            const regex = /\{\{(.*?)\}\}/g;
            let match;
            const foundConditions = new Set();

            while ((match = regex.exec(sql)) !== null) {
                const paramName = match[1].trim();
                if (foundConditions.has(paramName)) continue;
                foundConditions.add(paramName);

                const isSearchable = paramName.includes('编码') || paramName.includes('名称');

                const conditionHtml = `
                    <div class="flex items-center space-x-2" data-param-name="${paramName}">
                        <label class="w-1/4 text-sm">${paramName}</label>
                        <select class="condition-op bg-gray-700 border border-gray-600 rounded-lg p-2.5 text-sm w-1/4">
                            <option>等于</option><option>不等于</option><option>包含</option><option>IN</option>
                        </select>
                        <div class="relative w-1/2">
                            <input type="text" class="condition-value bg-gray-700 border border-gray-600 rounded-lg p-2.5 text-sm w-full pr-10">
                            ${isSearchable ? `<button onclick="openModal('search-modal')" class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-white"><i class="fas fa-search"></i></button>` : ''}
                        </div>
                    </div>
                `;
                conditionsContainer.insertAdjacentHTML('beforeend', conditionHtml);
            }
            if (foundConditions.size === 0) {
                conditionsContainer.innerHTML = '<p class="text-gray-500">该模板没有可配置的条件。</p>';
            }
        }

        // --- Edit and Delete Logic ---
        async function editRule(ruleName) {
            try {
                const response = await fetch(`/api/rules/${ruleName}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const rule = await response.json();

                // 设置编辑状态
                isEditingExistingRule = true;
                originalRuleName = rule.name;

                // 填充规则名称
                document.getElementById('rule-name').value = rule.name;

                // 填充规则属性
                if (rule.rule_type) {
                    document.getElementById('rule-type').value = rule.rule_type;
                }
                if (rule.patient_type) {
                    document.getElementById('patient-type').value = rule.patient_type;
                }
                if (rule.match_method) {
                    document.getElementById('match-method').value = rule.match_method;
                }
                if (rule.database_type) {
                    document.getElementById('database-type').value = rule.database_type;
                }
                if (rule.template_id) {
                    document.getElementById('rule-template').value = rule.template_id;
                }

                // 设置SQL内容
                setSqlContent(rule.content);

                // 清空模板推荐
                document.getElementById('template-recommendations').innerHTML = '<p class="text-gray-500 text-sm">点击"获取推荐"按钮获取智能模板推荐</p>';
                document.getElementById('conditions-container').innerHTML = '<p class="text-gray-500">当前为编辑模式，可直接修改SQL内容</p>';

                // 自动切换到编辑模式以便用户修改
                if (!isEditMode) {
                    toggleEditMode();
                }

                // 更新保存按钮文本
                const saveButton = document.getElementById('save-rule-btn');
                saveButton.innerHTML = '<i class="fas fa-save mr-2"></i> 更新规则';

                // 滚动到编辑器顶部
                window.scrollTo({ top: 0, behavior: 'smooth' });

                alert(`已加载规则 "${rule.name}" 进行编辑。您可以直接在编辑器中修改SQL内容。`);
            } catch (error) {
                console.error('Error fetching rule for editing:', error);
                alert('加载规则进行编辑时出错: ' + error.message);
            }
        }

        function resetEditingState() {
            isEditingExistingRule = false;
            originalRuleName = null;

            // 恢复保存按钮文本
            const saveButton = document.getElementById('save-rule-btn');
            saveButton.innerHTML = '<i class="fas fa-check-circle mr-2"></i> 保存规则';
        }

        async function deleteRule(ruleName) {
            if (!confirm(`您确定要删除规则 “${ruleName}” 吗？此操作无法撤销。`)) {
                return;
            }

            try {
                const response = await fetch(`/api/rules/${ruleName}`, {
                    method: 'DELETE',
                });
                const result = await response.json();
                if (response.ok) {
                    alert(result.message);
                    await loadSavedRules(); // Refresh the list
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                console.error('Error deleting rule:', error);
                alert(`删除规则时出错: ${error.message}`);
            }
        }

        // --- Save Rule Logic ---
        async function saveRule() {
            const ruleName = document.getElementById('rule-name').value.trim();
            const sqlContent = getCurrentSqlContent();

            if (!ruleName) {
                alert('请输入规则名称！');
                return;
            }

            if (!sqlContent || sqlContent.trim() === '' || sqlContent.startsWith('-- 在左侧表单中填写信息以生成SQL...')) {
                alert('没有可保存的SQL内容！请先选择模板并填写条件，或直接在编辑模式下编写SQL。');
                return;
            }

            // Validate rule attributes
            const attributeErrors = validateRuleAttributes();
            if (attributeErrors.length > 0) {
                alert('规则属性验证失败:\n' + attributeErrors.join('\n'));
                return;
            }

            // 确保内容是字符串类型
            const cleanSqlContent = String(sqlContent).trim();

            // Collect rule attributes
            const ruleAttributes = {
                name: ruleName,
                content: cleanSqlContent,
                description: `通过规则编辑器${isEditingExistingRule ? '更新' : '创建'}的规则`,
                category: 'manual',
                rule_type: document.getElementById('rule-type').value || null,
                patient_type: document.getElementById('patient-type').value || null,
                match_method: document.getElementById('match-method').value || null,
                database_type: document.getElementById('database-type').value || 'postgresql',
                template_id: document.getElementById('rule-template').value || null
            };

            console.log('准备保存规则:', {
                ...ruleAttributes,
                isEditing: isEditingExistingRule,
                originalName: originalRuleName
            });

            try {
                let response;

                if (isEditingExistingRule && originalRuleName) {
                    // 更新现有规则
                    response = await fetch(`/api/rules/${originalRuleName}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(ruleAttributes),
                    });
                } else {
                    // 创建新规则
                    response = await fetch('/api/rules', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(ruleAttributes),
                    });
                }

                const result = await response.json();

                if (response.ok) {
                    const action = isEditingExistingRule ? '更新' : '创建';
                    alert(`成功: 规则 "${ruleName}" ${action}成功！`);

                    // 清空表单和重置状态
                    document.getElementById('rule-name').value = '';
                    document.getElementById('rule-template').value = '';
                    setSqlContent('-- 在左侧表单中填写信息以生成SQL...');
                    document.getElementById('conditions-container').innerHTML = '<p class="text-gray-500">请先选择一个SQL模板</p>';

                    // 重置编辑状态
                    resetEditingState();

                    // 如果在编辑模式，切换回预览模式
                    if (isEditMode) {
                        toggleEditMode();
                    }

                    // 刷新规则列表
                    await loadSavedRules();
                } else {
                    alert(`错误: ${result.message || '保存失败'}`);
                }
            } catch (error) {
                console.error('Error saving rule:', error);
                alert('保存规则时发生网络错误: ' + error.message);
            }
        }

        // --- Rule Management Logic ---
        async function loadSavedRules() {
            try {
                const response = await fetch('/api/rules');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                const tableBody = document.getElementById('rules-table-body');
                tableBody.innerHTML = ''; // Clear existing rows

                // 处理新的API响应格式
                const rules = data.rules || data;

                if (!rules || rules.length === 0) {
                    tableBody.innerHTML = '<tr><td colspan="5" class="text-center py-4">未找到任何已保存的规则。</td></tr>';
                    return;
                }

                rules.forEach(rule => {
                    const createdAt = new Date(rule.created_at * 1000).toLocaleDateString();
                    const statusClass = rule.status === 'active' ? 'bg-green-900 text-green-300' : 'bg-red-900 text-red-300';
                    const statusText = rule.status === 'active' ? '已启用' : '已禁用';
                    const categoryText = rule.category || '手动创建';

                    const row = `
                        <tr class="bg-gray-800 border-b border-gray-700 hover:bg-gray-700">
                            <td class="px-6 py-4 font-medium text-white whitespace-nowrap" title="${rule.description || ''}">${rule.name}</td>
                            <td class="px-6 py-4">${categoryText}</td>
                            <td class="px-6 py-4"><span class="${statusClass} text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full">${statusText}</span></td>
                            <td class="px-6 py-4">${createdAt}</td>
                            <td class="px-6 py-4 text-center">
                                <button onclick="editRule('${rule.name}')" class="font-medium text-blue-500 hover:underline">编辑</button>
                                <button onclick="deleteRule('${rule.name}')" class="font-medium text-red-500 hover:underline ml-4">删除</button>
                            </td>
                        </tr>
                    `;
                    tableBody.innerHTML += row;
                });

                // 显示分页信息（如果有的话）
                if (data.pagination) {
                    console.log(`加载了 ${rules.length} 个规则，总共 ${data.pagination.total_count} 个`);
                }

            } catch (error) {
                console.error('Error fetching saved rules:', error);
                const tableBody = document.getElementById('rules-table-body');
                tableBody.innerHTML = '<tr><td colspan="5" class="text-center py-4 text-red-400">加载规则失败: ' + error.message + '</td></tr>';
            }
        }

        // --- SQL Editor Mode Management ---
        let isEditMode = false;
        let isEditingExistingRule = false;
        let originalRuleName = null;

        function toggleEditMode() {
            const sqlPreview = document.getElementById('sql-preview');
            const sqlEditor = document.getElementById('sql-editor');
            const sqlOutput = document.getElementById('sql-output');
            const editModeText = document.getElementById('edit-mode-text');
            const toggleButton = document.getElementById('toggle-edit-mode');

            if (isEditMode) {
                // 切换到预览模式
                const editorContent = sqlEditor.value;
                sqlOutput.textContent = editorContent;
                hljs.highlightElement(sqlOutput);

                sqlPreview.style.display = 'block';
                sqlEditor.style.display = 'none';
                editModeText.textContent = '编辑模式';
                toggleButton.innerHTML = '<i class="fas fa-edit mr-2"></i> <span id="edit-mode-text">编辑模式</span>';
                isEditMode = false;
            } else {
                // 切换到编辑模式
                const previewContent = sqlOutput.textContent || sqlOutput.innerText || '';
                sqlEditor.value = previewContent;

                sqlPreview.style.display = 'none';
                sqlEditor.style.display = 'block';
                sqlEditor.focus();
                editModeText.textContent = '预览模式';
                toggleButton.innerHTML = '<i class="fas fa-eye mr-2"></i> <span id="edit-mode-text">预览模式</span>';
                isEditMode = true;
            }
        }

        function getCurrentSqlContent() {
            if (isEditMode) {
                return document.getElementById('sql-editor').value;
            } else {
                return document.getElementById('sql-output').textContent || document.getElementById('sql-output').innerText || '';
            }
        }

        function setSqlContent(content) {
            const sqlOutput = document.getElementById('sql-output');
            const sqlEditor = document.getElementById('sql-editor');

            sqlOutput.textContent = content;
            sqlEditor.value = content;

            if (!isEditMode) {
                hljs.highlightElement(sqlOutput);
            }
        }

        // --- Rule Attribute Management Logic ---
        async function loadRuleAttributeOptions() {
            try {
                const response = await fetch('/api/rules/attribute-options');
                if (!response.ok) {
                    throw new Error('Failed to fetch attribute options');
                }
                const data = await response.json();
                const options = data.data;

                // Populate rule type options
                const ruleTypeSelect = document.getElementById('rule-type');
                ruleTypeSelect.innerHTML = '<option value="">自动检测</option>';
                options.rule_types.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type.value;
                    option.textContent = type.label;
                    ruleTypeSelect.appendChild(option);
                });

            } catch (error) {
                console.error('Error loading attribute options:', error);
            }
        }

        async function analyzeRuleName() {
            const ruleName = document.getElementById('rule-name').value.trim();
            
            if (!ruleName) {
                alert('请先输入规则名称！');
                return;
            }

            try {
                const response = await fetch('/api/rules/analyze-name', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        rule_name: ruleName
                    }),
                });

                if (!response.ok) {
                    throw new Error('Failed to analyze rule name');
                }

                const result = await response.json();
                const analysis = result.data;

                // Update form fields with detected attributes
                if (analysis.detected_rule_type) {
                    document.getElementById('rule-type').value = analysis.detected_rule_type;
                }
                if (analysis.detected_patient_type) {
                    document.getElementById('patient-type').value = analysis.detected_patient_type;
                }
                if (analysis.detected_match_method) {
                    document.getElementById('match-method').value = analysis.detected_match_method;
                }
                if (analysis.suggested_database_type) {
                    document.getElementById('database-type').value = analysis.suggested_database_type;
                }

                // Show analysis results
                const confidence = Math.round(analysis.confidence * 100);
                alert(`智能分析完成！\n检测置信度: ${confidence}%\n\n检测结果:\n` +
                      `规则类型: ${analysis.detected_rule_type || '未检测到'}\n` +
                      `适用范围: ${translatePatientType(analysis.detected_patient_type) || '未检测到'}\n` +
                      `匹配方式: ${translateMatchMethod(analysis.detected_match_method) || '未检测到'}\n` +
                      `建议数据库: ${analysis.suggested_database_type || 'PostgreSQL'}`);

            } catch (error) {
                console.error('Error analyzing rule name:', error);
                alert('分析规则名称时出错: ' + error.message);
            }
        }

        async function getTemplateRecommendations() {
            const ruleName = document.getElementById('rule-name').value.trim();
            const ruleType = document.getElementById('rule-type').value;
            const patientType = document.getElementById('patient-type').value;
            const matchMethod = document.getElementById('match-method').value;
            const databaseType = document.getElementById('database-type').value;

            if (!ruleName) {
                alert('请先输入规则名称！');
                return;
            }

            try {
                const response = await fetch('/api/templates/recommend', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        rule_name: ruleName,
                        rule_type: ruleType || null,
                        patient_type: patientType || null,
                        match_method: matchMethod || null,
                        database_type: databaseType || 'postgresql'
                    }),
                });

                if (!response.ok) {
                    throw new Error('Failed to get template recommendations');
                }

                const result = await response.json();
                const recommendations = result.data;

                // Display recommendations
                displayTemplateRecommendations(recommendations);

            } catch (error) {
                console.error('Error getting template recommendations:', error);
                alert('获取模板推荐时出错: ' + error.message);
            }
        }

        function displayTemplateRecommendations(recommendations) {
            const container = document.getElementById('template-recommendations');
            
            if (!recommendations || recommendations.length === 0) {
                container.innerHTML = '<p class="text-gray-500 text-sm">未找到匹配的模板推荐</p>';
                return;
            }

            let html = '<div class="space-y-2">';
            html += '<h4 class="text-sm font-medium text-white mb-2">推荐模板 (按匹配度排序):</h4>';
            
            recommendations.slice(0, 5).forEach((rec, index) => {
                const score = Math.round(rec.score);
                const reasons = rec.reasons.join(', ');
                
                html += `
                    <div class="bg-gray-700 rounded-lg p-3 cursor-pointer hover:bg-gray-600 transition-colors"
                         onclick="selectRecommendedTemplate('${rec.template.id}')">
                        <div class="flex justify-between items-start">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-white">${rec.template.description || rec.template.id}</div>
                                <div class="text-xs text-gray-400 mt-1">${reasons}</div>
                            </div>
                            <div class="text-xs text-green-400 font-medium ml-2">匹配度: ${score}%</div>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            container.innerHTML = html;
        }

        function selectRecommendedTemplate(templateId) {
            // Set the template in the dropdown
            document.getElementById('rule-template').value = templateId;
            
            // Trigger the change event to load the template
            const event = new Event('change');
            document.getElementById('rule-template').dispatchEvent(event);
            
            // Show success message
            const templateName = document.getElementById('rule-template').selectedOptions[0].text;
            alert(`已选择推荐模板: ${templateName}`);
        }

        function translatePatientType(patientType) {
            const translations = {
                'inpatient': '住院',
                'outpatient': '门诊',
                'general': '通用'
            };
            return translations[patientType] || patientType;
        }

        function translateMatchMethod(matchMethod) {
            const translations = {
                'name': '按名称',
                'code': '按编码'
            };
            return translations[matchMethod] || matchMethod;
        }

        function validateRuleAttributes() {
            const ruleType = document.getElementById('rule-type').value;
            const patientType = document.getElementById('patient-type').value;
            const matchMethod = document.getElementById('match-method').value;
            const databaseType = document.getElementById('database-type').value;

            const errors = [];

            // Add any specific validation rules here
            if (ruleType && !['超频次', '重复收费', '限性别', '限年龄', '病例提取', '多项合并', '组套收费', '禁忌药物', '诊断项目不匹配', '超用药金额', '超合理用药疗程', '限医保等级'].includes(ruleType)) {
                errors.push('无效的规则类型');
            }

            if (patientType && !['inpatient', 'outpatient', 'general'].includes(patientType)) {
                errors.push('无效的适用范围');
            }

            if (matchMethod && !['name', 'code'].includes(matchMethod)) {
                errors.push('无效的匹配方式');
            }

            if (databaseType && !['postgresql', 'oracle'].includes(databaseType)) {
                errors.push('无效的数据库类型');
            }

            return errors;
        }

        // --- Highlight & Copy Logic ---
        document.addEventListener('DOMContentLoaded', () => {
            hljs.highlightAll();
            fetchSqlTemplates();
            loadSavedRules(); // Load rules on page load
            loadRuleAttributeOptions(); // Load attribute options

            document.getElementById('save-rule-btn').addEventListener('click', async () => {
                await saveRule();
            });

            // Add event listeners for rule attribute buttons
            document.getElementById('analyze-rule-btn').addEventListener('click', analyzeRuleName);
            document.getElementById('get-recommendations-btn').addEventListener('click', getTemplateRecommendations);

            document.getElementById('new-rule-btn').addEventListener('click', () => {
                // 清空表单
                document.getElementById('rule-name').value = '';
                document.getElementById('rule-template').value = '';
                
                // 清空规则属性
                document.getElementById('rule-type').value = '';
                document.getElementById('patient-type').value = '';
                document.getElementById('match-method').value = '';
                document.getElementById('database-type').value = 'postgresql';
                
                // 清空模板推荐
                document.getElementById('template-recommendations').innerHTML = '<p class="text-gray-500 text-sm">点击"获取推荐"按钮获取智能模板推荐</p>';
                
                setSqlContent('-- 在左侧表单中填写信息以生成SQL...');
                document.getElementById('conditions-container').innerHTML = '<p class="text-gray-500">请先选择一个SQL模板</p>';

                // 重置编辑状态
                resetEditingState();

                // 如果在编辑模式，切换回预览模式
                if (isEditMode) {
                    toggleEditMode();
                }

                alert('已清空表单，可以开始创建新规则。');
            });

            document.getElementById('toggle-edit-mode').addEventListener('click', toggleEditMode);
        });

        document.getElementById('copy-sql').addEventListener('click', () => {
            const content = getCurrentSqlContent();
            navigator.clipboard.writeText(content);

            // 显示复制成功提示
            const copyButton = document.getElementById('copy-sql');
            const originalText = copyButton.innerHTML;
            copyButton.innerHTML = '<i class="fas fa-check mr-2"></i> 已复制';
            setTimeout(() => {
                copyButton.innerHTML = originalText;
            }, 2000);
        });

    </script>
</body>

</html>