"""
Intelligent template selection service for automatic template recommendation.
"""
import re
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from models.rule import TemplateInfo, Rule
from services.template_service import TemplateManagementService, TemplateCategory
from utils.error_handler import BusinessError, SystemError


@dataclass
class RuleAttributes:
    """Rule attributes for template selection."""
    rule_name: str
    rule_type: Optional[str] = None  # 超频次、重复收费、限性别等
    patient_type: Optional[str] = None  # inpatient, outpatient, general
    match_method: Optional[str] = None  # name, code
    database_type: Optional[str] = None  # postgresql, oracle
    
    def __post_init__(self):
        # Normalize values
        if self.patient_type:
            self.patient_type = self.patient_type.lower()
        if self.match_method:
            self.match_method = self.match_method.lower()
        if self.database_type:
            self.database_type = self.database_type.lower()


@dataclass
class TemplateRecommendation:
    """Template recommendation with score and reasoning."""
    template: TemplateInfo
    score: float
    reasons: List[str]
    category_match: bool = False
    name_match: bool = False
    type_match: bool = False


class IntelligentTemplateService:
    """Service for intelligent template selection and recommendation."""
    
    def __init__(self):
        self.template_service = TemplateManagementService()
        
        # Rule type keywords mapping
        self.rule_type_keywords = {
            "超频次": ["超每日", "超每周", "超每月", "超每年", "超两周", "超住院", "超天数", "频次"],
            "重复收费": ["重复收费", "重复", "收费"],
            "限性别": ["限性别", "性别"],
            "限年龄": ["限年龄", "年龄"],
            "病例提取": ["病例提取", "提取"],
            "多项合并": ["多项合并", "合并"],
            "组套收费": ["组套收费", "组套"],
            "禁忌药物": ["禁忌药物", "禁忌"],
            "诊断项目不匹配": ["诊断项目", "不匹配"],
            "超用药金额": ["超用药金额", "用药金额"],
            "超合理用药疗程": ["超合理用药", "疗程"],
            "限医保等级": ["限医保等级", "医保等级"]
        }
        
        # Patient type keywords
        self.patient_type_keywords = {
            "inpatient": ["住院", "住院期间", "入院", "出院"],
            "outpatient": ["门诊", "门诊期间"]
        }
        
        # Match method keywords
        self.match_method_keywords = {
            "name": ["名称", "项目名称", "医保名称"],
            "code": ["编码", "项目编码", "医保编码"]
        }
    
    def recommend_templates(self, rule_attributes: RuleAttributes, 
                          max_recommendations: int = 10) -> List[TemplateRecommendation]:
        """
        Recommend templates based on rule attributes.
        
        Args:
            rule_attributes: Rule attributes for template matching
            max_recommendations: Maximum number of recommendations to return
            
        Returns:
            List of template recommendations sorted by score
        """
        try:
            # Get all available templates
            all_templates = self.template_service.get_templates()
            
            # Score and filter templates
            recommendations = []
            
            for template in all_templates:
                score, reasons = self._calculate_template_score(template, rule_attributes)
                
                if score > 0:  # Only include templates with positive scores
                    recommendation = TemplateRecommendation(
                        template=template,
                        score=score,
                        reasons=reasons,
                        category_match=self._check_category_match(template, rule_attributes),
                        name_match=self._check_name_match(template, rule_attributes),
                        type_match=self._check_type_match(template, rule_attributes)
                    )
                    recommendations.append(recommendation)
            
            # Sort by score (descending) and return top recommendations
            recommendations.sort(key=lambda x: x.score, reverse=True)
            return recommendations[:max_recommendations]
            
        except Exception as e:
            raise SystemError(f"Failed to recommend templates: {str(e)}")
    
    def filter_templates_by_attributes(self, rule_attributes: RuleAttributes) -> List[TemplateInfo]:
        """
        Filter templates based on rule attributes.
        
        Args:
            rule_attributes: Rule attributes for filtering
            
        Returns:
            List of filtered templates
        """
        try:
            filtered_templates = []
            
            # Start with all templates
            all_templates = self.template_service.get_templates()
            
            for template in all_templates:
                if self._matches_attributes(template, rule_attributes):
                    filtered_templates.append(template)
            
            return filtered_templates
            
        except Exception as e:
            raise SystemError(f"Failed to filter templates: {str(e)}")
    
    def get_template_recommendations_by_name(self, rule_name: str, 
                                           max_recommendations: int = 5) -> List[TemplateRecommendation]:
        """
        Get template recommendations based only on rule name.
        
        Args:
            rule_name: Name of the rule
            max_recommendations: Maximum number of recommendations
            
        Returns:
            List of template recommendations
        """
        # Extract attributes from rule name
        rule_attributes = self._extract_attributes_from_name(rule_name)
        
        return self.recommend_templates(rule_attributes, max_recommendations)
    
    def _calculate_template_score(self, template: TemplateInfo, 
                                rule_attributes: RuleAttributes) -> Tuple[float, List[str]]:
        """
        Calculate matching score for a template against rule attributes.
        
        Returns:
            Tuple of (score, reasons)
        """
        score = 0.0
        reasons = []
        
        # Database type matching (high priority)
        if rule_attributes.database_type and template.database_type:
            if template.database_type == rule_attributes.database_type:
                score += 30.0
                reasons.append(f"数据库类型匹配: {template.database_type}")
            elif template.database_type == "general":
                score += 15.0
                reasons.append("通用数据库模板")
        elif template.database_type == "general":
            score += 10.0
            reasons.append("通用数据库模板")
        
        # Patient type matching (high priority)
        if rule_attributes.patient_type and template.patient_type:
            if template.patient_type == rule_attributes.patient_type:
                score += 25.0
                reasons.append(f"患者类型匹配: {self._translate_patient_type(template.patient_type)}")
            elif template.patient_type == "general":
                score += 12.0
                reasons.append("通用患者类型模板")
        elif template.patient_type == "general":
            score += 8.0
            reasons.append("通用患者类型模板")
        
        # Match method scoring (medium priority)
        if rule_attributes.match_method:
            if self._check_match_method_compatibility(template, rule_attributes.match_method):
                score += 20.0
                reasons.append(f"匹配方式兼容: {self._translate_match_method(rule_attributes.match_method)}")
        
        # Rule type matching (medium priority)
        if rule_attributes.rule_type:
            type_score = self._calculate_rule_type_score(template, rule_attributes.rule_type)
            if type_score > 0:
                score += type_score
                reasons.append(f"规则类型匹配: {rule_attributes.rule_type}")
        
        # Name similarity matching (lower priority)
        name_score = self._calculate_name_similarity_score(template, rule_attributes.rule_name)
        if name_score > 0:
            score += name_score
            reasons.append(f"名称相似度匹配")
        
        # Template popularity/quality bonus (based on category)
        category_bonus = self._get_category_bonus(template.category)
        if category_bonus > 0:
            score += category_bonus
            reasons.append("推荐模板类别")
        
        return score, reasons
    
    def _matches_attributes(self, template: TemplateInfo, rule_attributes: RuleAttributes) -> bool:
        """Check if template matches the given attributes."""
        
        # Database type filter
        if rule_attributes.database_type:
            if template.database_type and template.database_type != "general":
                if template.database_type != rule_attributes.database_type:
                    return False
        
        # Patient type filter
        if rule_attributes.patient_type:
            if template.patient_type and template.patient_type != "general":
                if template.patient_type != rule_attributes.patient_type:
                    return False
        
        # Match method filter
        if rule_attributes.match_method:
            if not self._check_match_method_compatibility(template, rule_attributes.match_method):
                return False
        
        return True
    
    def _extract_attributes_from_name(self, rule_name: str) -> RuleAttributes:
        """Extract rule attributes from rule name."""
        rule_name_lower = rule_name.lower()
        
        # Extract rule type
        rule_type = None
        for type_name, keywords in self.rule_type_keywords.items():
            if any(keyword in rule_name for keyword in keywords):
                rule_type = type_name
                break
        
        # Extract patient type
        patient_type = None
        for type_name, keywords in self.patient_type_keywords.items():
            if any(keyword in rule_name for keyword in keywords):
                patient_type = type_name
                break
        
        # Extract match method
        match_method = None
        for method_name, keywords in self.match_method_keywords.items():
            if any(keyword in rule_name for keyword in keywords):
                match_method = method_name
                break
        
        return RuleAttributes(
            rule_name=rule_name,
            rule_type=rule_type,
            patient_type=patient_type,
            match_method=match_method
        )
    
    def _check_category_match(self, template: TemplateInfo, rule_attributes: RuleAttributes) -> bool:
        """Check if template category matches rule attributes."""
        if not template.category:
            return False
        
        category_lower = template.category.lower()
        
        # Check database type match
        if rule_attributes.database_type:
            if rule_attributes.database_type in category_lower:
                return True
        
        # Check patient type match
        if rule_attributes.patient_type:
            if rule_attributes.patient_type in category_lower:
                return True
        
        return False
    
    def _check_name_match(self, template: TemplateInfo, rule_attributes: RuleAttributes) -> bool:
        """Check if template name matches rule attributes."""
        template_name = template.description or template.id
        rule_name = rule_attributes.rule_name
        
        # Simple keyword matching
        rule_keywords = rule_name.split()
        template_keywords = template_name.split()
        
        matches = 0
        for rule_keyword in rule_keywords:
            if any(rule_keyword in template_keyword for template_keyword in template_keywords):
                matches += 1
        
        return matches > 0
    
    def _check_type_match(self, template: TemplateInfo, rule_attributes: RuleAttributes) -> bool:
        """Check if template type matches rule attributes."""
        if not rule_attributes.rule_type:
            return False
        
        template_name = template.description or template.id
        
        # Check if rule type keywords appear in template name
        keywords = self.rule_type_keywords.get(rule_attributes.rule_type, [])
        return any(keyword in template_name for keyword in keywords)
    
    def _check_match_method_compatibility(self, template: TemplateInfo, match_method: str) -> bool:
        """Check if template is compatible with the specified match method."""
        if not template.category:
            return True  # Assume compatibility if no category info
        
        category_lower = template.category.lower()
        
        if match_method == "name":
            return "name" in category_lower or "名称" in category_lower
        elif match_method == "code":
            return "code" in category_lower or "编码" in category_lower
        
        return True  # Default to compatible
    
    def _calculate_rule_type_score(self, template: TemplateInfo, rule_type: str) -> float:
        """Calculate score based on rule type matching."""
        template_name = template.description or template.id
        
        keywords = self.rule_type_keywords.get(rule_type, [])
        
        matches = 0
        for keyword in keywords:
            if keyword in template_name:
                matches += 1
        
        # Score based on number of keyword matches
        if matches > 0:
            return min(15.0, matches * 5.0)
        
        return 0.0
    
    def _calculate_name_similarity_score(self, template: TemplateInfo, rule_name: str) -> float:
        """Calculate score based on name similarity."""
        template_name = template.description or template.id
        
        # Simple similarity based on common characters/words
        rule_chars = set(rule_name)
        template_chars = set(template_name)
        
        if not rule_chars or not template_chars:
            return 0.0
        
        common_chars = rule_chars.intersection(template_chars)
        similarity = len(common_chars) / max(len(rule_chars), len(template_chars))
        
        # Convert to score (max 10 points)
        return similarity * 10.0
    
    def _get_category_bonus(self, category: Optional[str]) -> float:
        """Get bonus score based on template category quality/popularity."""
        if not category:
            return 0.0
        
        # Prefer more specific categories
        category_scores = {
            "rule_pg_name_inpatient": 5.0,
            "rule_pg_name_outpatient": 5.0,
            "rule_pg_code_inpatient": 5.0,
            "rule_pg_code_outpatient": 5.0,
            "rule_oracle_name_inpatient": 4.0,
            "rule_oracle_code_inpatient": 4.0,
            "rule-pg-code": 3.0,
            "rule-general": 2.0,
            "rule": 1.0,
            "manual": 0.5,
            "excel": 0.5
        }
        
        return category_scores.get(category, 0.0)
    
    def _translate_patient_type(self, patient_type: str) -> str:
        """Translate patient type to Chinese."""
        translations = {
            "inpatient": "住院",
            "outpatient": "门诊",
            "general": "通用"
        }
        return translations.get(patient_type, patient_type)
    
    def _translate_match_method(self, match_method: str) -> str:
        """Translate match method to Chinese."""
        translations = {
            "name": "按名称",
            "code": "按编码"
        }
        return translations.get(match_method, match_method)
    
    def get_filtered_templates(self, database_type: Optional[str] = None,
                             patient_type: Optional[str] = None,
                             match_method: Optional[str] = None,
                             rule_type: Optional[str] = None) -> List[TemplateInfo]:
        """
        Get templates filtered by multiple criteria.
        
        Args:
            database_type: Database type filter (postgresql, oracle)
            patient_type: Patient type filter (inpatient, outpatient, general)
            match_method: Match method filter (name, code)
            rule_type: Rule type filter
            
        Returns:
            List of filtered templates
        """
        rule_attributes = RuleAttributes(
            rule_name="",
            rule_type=rule_type,
            patient_type=patient_type,
            match_method=match_method,
            database_type=database_type
        )
        
        return self.filter_templates_by_attributes(rule_attributes)
    
    def analyze_rule_attributes(self, rule_name: str) -> Dict[str, Any]:
        """
        Analyze rule name and return detected attributes.
        
        Args:
            rule_name: Name of the rule to analyze
            
        Returns:
            Dictionary containing detected attributes
        """
        attributes = self._extract_attributes_from_name(rule_name)
        
        return {
            "rule_name": rule_name,
            "detected_rule_type": attributes.rule_type,
            "detected_patient_type": attributes.patient_type,
            "detected_match_method": attributes.match_method,
            "suggested_database_type": "postgresql",  # Default suggestion
            "confidence": self._calculate_detection_confidence(attributes)
        }
    
    def _calculate_detection_confidence(self, attributes: RuleAttributes) -> float:
        """Calculate confidence score for attribute detection."""
        confidence = 0.0
        
        if attributes.rule_type:
            confidence += 0.4
        if attributes.patient_type:
            confidence += 0.3
        if attributes.match_method:
            confidence += 0.3
        
        return min(1.0, confidence)