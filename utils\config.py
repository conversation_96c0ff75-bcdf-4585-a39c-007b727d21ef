"""
Configuration management module for the rule management system.
"""
import os
from typing import Dict, Any


class Config:
    """Application configuration class."""
    
    def __init__(self):
        self.BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.OUTPUT_DIR = os.path.join(self.BASE_DIR, 'output')
        self.TEMPLATES_DIR = os.path.join(self.BASE_DIR, 'templates')
        self.TEMPLATES_RULE_DIR = os.path.join(self.TEMPLATES_DIR, 'rule')
        
        # Flask configuration
        self.FLASK_CONFIG = {
            'TEMPLATES_AUTO_RELOAD': True,
            'SEND_FILE_MAX_AGE_DEFAULT': 0,
            'DEBUG': os.getenv('DEBUG', 'True').lower() == 'true'
        }
        
        # Ensure required directories exist
        self._ensure_directories()
    
    def _ensure_directories(self):
        """Ensure all required directories exist."""
        directories = [
            self.OUTPUT_DIR,
            self.TEMPLATES_DIR,
            self.TEMPLATES_RULE_DIR
        ]
        
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
    
    def get_flask_config(self) -> Dict[str, Any]:
        """Get Flask configuration dictionary."""
        return self.FLASK_CONFIG.copy()


# Global configuration instance
config = Config()