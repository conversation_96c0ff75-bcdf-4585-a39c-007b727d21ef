"""
Main Flask application with layered architecture.
"""
from flask import Flask, render_template, request
from utils.config import config
from utils.error_handler import register_error_handlers
from controllers.rule_controller import RuleController
from controllers.template_controller import TemplateController
from controllers.intelligent_template_controller import intelligent_template_bp
from controllers.intelligent_workflow_controller import intelligent_workflow_bp
from controllers.template_selector_controller import template_selector_bp

# Initialize Flask app
app = Flask(__name__, template_folder='page')

# Apply configuration
app.config.update(config.get_flask_config())

# Register error handlers
register_error_handlers(app)

# Register blueprints
app.register_blueprint(intelligent_template_bp)
app.register_blueprint(intelligent_workflow_bp)
app.register_blueprint(template_selector_bp)

# Initialize controllers
rule_controller = RuleController()
template_controller = TemplateController()


@app.route('/')
def index():
    """Renders the main application page."""
    return render_template('temp_rule_editor.html')


# Rule management endpoints
@app.route('/api/rules', methods=['GET', 'POST'])
def handle_rules_collection():
    """Handle rule collection operations."""
    if request.method == 'POST':
        return rule_controller.create_rule()
    return rule_controller.list_rules()


@app.route('/api/rules/search')
def search_rules():
    """Handle rule search operations."""
    return rule_controller.search_rules()


@app.route('/api/rules/<string:rule_name>', methods=['GET', 'PUT', 'DELETE'])
def handle_single_rule(rule_name):
    """Handle single rule operations."""
    if request.method == 'GET':
        return rule_controller.get_rule(rule_name)
    elif request.method == 'PUT':
        return rule_controller.update_rule(rule_name)
    elif request.method == 'DELETE':
        return rule_controller.delete_rule(rule_name)


@app.route('/api/rules/<string:rule_name>/restore', methods=['POST'])
def restore_rule(rule_name):
    """Handle rule restoration."""
    return rule_controller.restore_rule(rule_name)


# Rule attribute management endpoints
@app.route('/api/rules/attribute-options')
def get_rule_attribute_options():
    """Get available rule attribute options."""
    return rule_controller.get_rule_attribute_options()


@app.route('/api/rules/analyze-name', methods=['POST'])
def analyze_rule_name():
    """Analyze rule name for attribute suggestions."""
    return rule_controller.analyze_rule_name()


@app.route('/api/rules/filter')
def filter_rules_by_attributes():
    """Filter rules by attributes."""
    return rule_controller.filter_rules_by_attributes()


@app.route('/api/rules/statistics')
def get_rule_statistics():
    """Get rule statistics."""
    return rule_controller.get_rule_statistics()


@app.route('/api/rules/validate-attributes', methods=['POST'])
def validate_rule_attributes():
    """Validate rule attributes."""
    return rule_controller.validate_rule_attributes()


# Template management endpoints
@app.route('/api/sql-templates')
def get_sql_templates_content():
    """Fetches the content of all SQL template files."""
    return template_controller.get_sql_templates()


@app.route('/api/templates/recommend', methods=['POST'])
def get_template_recommendations():
    """Get template recommendations based on rule attributes."""
    return template_controller.get_template_recommendations()

if __name__ == '__main__':
    app.run(debug=config.FLASK_CONFIG['DEBUG'], port=5000)
