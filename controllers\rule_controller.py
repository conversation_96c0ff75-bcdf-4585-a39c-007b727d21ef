"""
Rule management API controllers.
"""
from flask import request, jsonify
from services.rule_service import RuleManagementService
from models.rule import RuleData
from utils.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Val<PERSON>tionError, BusinessError


class RuleController:
    """Controller for rule management endpoints."""
    
    def __init__(self):
        self.rule_service = RuleManagementService()
    
    def list_rules(self):
        """Handle GET /api/rules - List all rules with optional search and pagination."""
        try:
            # Get query parameters
            search_query = request.args.get('search', '').strip()
            page = int(request.args.get('page', 1))
            page_size = int(request.args.get('page_size', 20))
            include_deleted = request.args.get('include_deleted', 'false').lower() == 'true'
            
            # Validate pagination parameters
            if page < 1:
                page = 1
            if page_size < 1 or page_size > 100:
                page_size = 20
            
            result = self.rule_service.list_rules(
                search_query=search_query if search_query else None,
                page=page,
                page_size=page_size,
                include_deleted=include_deleted
            )
            
            # Convert dataclass objects to dictionaries for JSON serialization
            rules_data = [
                {
                    'name': rule.name,
                    'created_at': rule.created_at,
                    'file_size': rule.file_size,
                    'status': rule.status,
                    'description': rule.description,
                    'category': rule.category,
                    'rule_type': rule.rule_type
                }
                for rule in result['rules']
            ]
            
            return jsonify({
                'rules': rules_data,
                'pagination': {
                    'total_count': result['total_count'],
                    'page': result['page'],
                    'page_size': result['page_size'],
                    'total_pages': result['total_pages'],
                    'has_next': result['has_next'],
                    'has_prev': result['has_prev']
                }
            })
            
        except ValueError as e:
            return ErrorHandler.handle_validation_error(
                ValidationError(f"Invalid pagination parameters: {str(e)}")
            )
        except Exception as e:
            return ErrorHandler.handle_generic_error(e)
    
    def create_rule(self):
        """Handle POST /api/rules - Create a new rule."""
        try:
            data = request.get_json()
            
            if not data:
                raise ValidationError("Request body is required")
            
            rule_name = data.get('name')
            rule_content = data.get('content')
            
            if not rule_name or not rule_content:
                raise ValidationError(
                    "Rule name and content are required",
                    details={
                        'missing_fields': [
                            field for field in ['name', 'content'] 
                            if not data.get(field)
                        ]
                    }
                )
            
            rule_data = RuleData(
                name=rule_name.strip(),
                content=rule_content.strip(),
                description=data.get('description'),
                category=data.get('category'),
                rule_type=data.get('rule_type'),
                policy_basis=data.get('policy_basis'),
                template_id=data.get('template_id'),
                parameters=data.get('parameters'),
                database_type=data.get('database_type'),
                created_by=data.get('created_by'),
                patient_type=data.get('patient_type'),
                match_method=data.get('match_method'),
                applicable_scope=data.get('applicable_scope')
            )
            
            result = self.rule_service.create_rule_with_validation(rule_data)
            
            return ErrorHandler.success_response(result['message'], data=result)
                
        except (ValidationError, BusinessError, Exception) as e:
            if isinstance(e, ValidationError):
                return ErrorHandler.handle_validation_error(e)
            elif isinstance(e, BusinessError):
                return ErrorHandler.handle_business_error(e)
            return ErrorHandler.handle_generic_error(e)
    
    def get_rule(self, rule_name):
        """Handle GET /api/rules/<rule_name> - Get a specific rule."""
        try:
            rule = self.rule_service.get_rule(rule_name)
            
            return jsonify({
                'name': rule.name,
                'content': rule.content,
                'created_at': rule.created_at,
                'file_size': rule.file_size,
                'status': rule.status
            })
            
        except Exception as e:
            return ErrorHandler.handle_generic_error(e)
    
    def delete_rule(self, rule_name):
        """Handle DELETE /api/rules/<rule_name> - Delete a rule."""
        try:
            # Check for logical delete parameter
            logical_delete = request.args.get('logical', 'true').lower() == 'true'
            
            result = self.rule_service.delete_rule(rule_name, logical_delete=logical_delete)
            
            return ErrorHandler.success_response(result['message'], data=result)
            
        except (BusinessError, Exception) as e:
            if isinstance(e, BusinessError):
                return ErrorHandler.handle_business_error(e)
            return ErrorHandler.handle_generic_error(e)
    
    def search_rules(self):
        """Handle GET /api/rules/search - Search rules."""
        try:
            query = request.args.get('q', '').strip()
            
            if not query:
                return ErrorHandler.handle_validation_error(
                    ValidationError("Search query parameter 'q' is required")
                )
            
            rules = self.rule_service.search_rules(query)
            
            # Convert dataclass objects to dictionaries for JSON serialization
            rules_data = [
                {
                    'name': rule.name,
                    'created_at': rule.created_at,
                    'file_size': rule.file_size,
                    'status': rule.status,
                    'description': rule.description,
                    'category': rule.category,
                    'rule_type': rule.rule_type
                }
                for rule in rules
            ]
            
            return jsonify({
                'query': query,
                'results': rules_data,
                'count': len(rules_data)
            })
            
        except Exception as e:
            return ErrorHandler.handle_generic_error(e)
    
    def update_rule(self, rule_name):
        """Handle PUT /api/rules/<rule_name> - Update an existing rule."""
        try:
            data = request.get_json()
            
            if not data:
                raise ValidationError("Request body is required")
            
            rule_content = data.get('content')
            
            if not rule_content:
                raise ValidationError(
                    "Rule content is required",
                    details={'missing_fields': ['content']}
                )
            
            rule_data = RuleData(
                name=data.get('name', rule_name).strip(),
                content=rule_content.strip(),
                description=data.get('description'),
                category=data.get('category'),
                rule_type=data.get('rule_type'),
                policy_basis=data.get('policy_basis'),
                template_id=data.get('template_id'),
                parameters=data.get('parameters'),
                database_type=data.get('database_type'),
                created_by=data.get('created_by'),
                patient_type=data.get('patient_type'),
                match_method=data.get('match_method'),
                applicable_scope=data.get('applicable_scope')
            )
            
            result = self.rule_service.update_rule_with_validation(rule_name, rule_data)
            
            return ErrorHandler.success_response(result['message'], data=result)
                
        except (ValidationError, BusinessError, Exception) as e:
            if isinstance(e, ValidationError):
                return ErrorHandler.handle_validation_error(e)
            elif isinstance(e, BusinessError):
                return ErrorHandler.handle_business_error(e)
            return ErrorHandler.handle_generic_error(e)
    
    def restore_rule(self, rule_name):
        """Handle POST /api/rules/<rule_name>/restore - Restore a deleted rule."""
        try:
            result = self.rule_service.restore_rule(rule_name)
            
            return ErrorHandler.success_response(result['message'], data=result)
            
        except (BusinessError, Exception) as e:
            if isinstance(e, BusinessError):
                return ErrorHandler.handle_business_error(e)
            return ErrorHandler.handle_generic_error(e)
    
    def get_rule_attribute_options(self):
        """Handle GET /api/rules/attribute-options - Get available attribute options."""
        try:
            options = self.rule_service.get_rule_attribute_options()
            
            return jsonify({
                'status': 'success',
                'data': options
            })
            
        except Exception as e:
            return ErrorHandler.handle_generic_error(e)
    
    def analyze_rule_name(self):
        """Handle POST /api/rules/analyze-name - Analyze rule name for attribute suggestions."""
        try:
            data = request.get_json()
            
            if not data or 'rule_name' not in data:
                raise ValidationError("rule_name is required in request body")
            
            rule_name = data['rule_name']
            analysis = self.rule_service.analyze_rule_name_for_attributes(rule_name)
            
            return jsonify({
                'status': 'success',
                'data': analysis
            })
            
        except Exception as e:
            return ErrorHandler.handle_generic_error(e)
    
    def filter_rules_by_attributes(self):
        """Handle GET /api/rules/filter - Filter rules by attributes."""
        try:
            rule_type = request.args.get('rule_type')
            patient_type = request.args.get('patient_type')
            match_method = request.args.get('match_method')
            database_type = request.args.get('database_type')
            
            rules = self.rule_service.get_rules_by_attributes(
                rule_type=rule_type,
                patient_type=patient_type,
                match_method=match_method,
                database_type=database_type
            )
            
            # Convert dataclass objects to dictionaries for JSON serialization
            rules_data = [
                {
                    'name': rule.name,
                    'created_at': rule.created_at,
                    'file_size': rule.file_size,
                    'status': rule.status,
                    'description': rule.description,
                    'category': rule.category,
                    'rule_type': rule.rule_type
                }
                for rule in rules
            ]
            
            return jsonify({
                'status': 'success',
                'data': {
                    'rules': rules_data,
                    'total_count': len(rules_data),
                    'filters': {
                        'rule_type': rule_type,
                        'patient_type': patient_type,
                        'match_method': match_method,
                        'database_type': database_type
                    }
                }
            })
            
        except Exception as e:
            return ErrorHandler.handle_generic_error(e)
    
    def get_rule_statistics(self):
        """Handle GET /api/rules/statistics - Get rule statistics."""
        try:
            stats = self.rule_service.get_rule_statistics()
            
            return jsonify({
                'status': 'success',
                'data': stats
            })
            
        except Exception as e:
            return ErrorHandler.handle_generic_error(e)
    
    def validate_rule_attributes(self):
        """Handle POST /api/rules/validate-attributes - Validate rule attributes."""
        try:
            data = request.get_json()
            
            if not data:
                raise ValidationError("Request body is required")
            
            # Create a temporary RuleData object for validation
            rule_data = RuleData(
                name=data.get('name', 'temp'),
                content=data.get('content', 'temp'),
                rule_type=data.get('rule_type'),
                patient_type=data.get('patient_type'),
                match_method=data.get('match_method'),
                database_type=data.get('database_type'),
                parameters=data.get('parameters')
            )
            
            errors = self.rule_service.validate_rule_attributes(rule_data)
            
            return jsonify({
                'status': 'success',
                'data': {
                    'is_valid': len(errors) == 0,
                    'errors': errors
                }
            })
            
        except Exception as e:
            return ErrorHandler.handle_generic_error(e)