<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modal Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .modal-backdrop {
            transition: opacity 0.3s ease;
        }
        .modal-content {
            transition: transform 0.3s ease;
        }
    </style>
</head>
<body class="bg-gray-900 text-white">
    <div class="container mx-auto p-8">
        <h1 class="text-3xl font-bold mb-8">弹窗测试</h1>
        
        <div class="space-x-4">
            <button onclick="openModal('test-modal')" 
                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg">
                打开测试弹窗
            </button>
            
            <button onclick="openModal('db-config-modal')" 
                    class="bg-gray-700 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg">
                数据库配置弹窗
            </button>
            
            <button onclick="openModal('workflow-wizard-modal')" 
                    class="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded-lg">
                智能向导弹窗
            </button>
        </div>
    </div>

    <!-- Test Modal -->
    <div id="test-modal" class="modal-backdrop fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center hidden opacity-0">
        <div class="modal-content bg-gray-800 rounded-lg shadow-xl p-8 w-full max-w-md transform scale-95">
            <h2 class="text-2xl font-bold text-white mb-6">测试弹窗</h2>
            <p class="text-gray-300 mb-6">这是一个测试弹窗，用于验证弹窗功能是否正常工作。</p>
            <div class="flex justify-end">
                <button onclick="closeModal('test-modal')" 
                        class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg">
                    关闭
                </button>
            </div>
        </div>
    </div>

    <!-- DB Config Modal -->
    <div id="db-config-modal" class="modal-backdrop fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center hidden opacity-0">
        <div class="modal-content bg-gray-800 rounded-lg shadow-xl p-8 w-full max-w-md transform scale-95">
            <h2 class="text-2xl font-bold text-white mb-6">数据库配置</h2>
            <div class="space-y-4">
                <div>
                    <label class="block mb-2 text-sm font-medium text-gray-300">数据库类型</label>
                    <select class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg block w-full p-2.5">
                        <option>PostgreSQL</option>
                        <option>Oracle</option>
                    </select>
                </div>
            </div>
            <div class="flex justify-end space-x-4 mt-8">
                <button onclick="closeModal('db-config-modal')" 
                        class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg">
                    取消
                </button>
                <button class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg">
                    保存
                </button>
            </div>
        </div>
    </div>

    <!-- Workflow Wizard Modal -->
    <div id="workflow-wizard-modal" class="modal-backdrop fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center hidden opacity-0">
        <div class="modal-content bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl h-3/4 transform scale-95 flex flex-col">
            <div class="flex justify-between items-center p-6 border-b border-gray-700">
                <h2 class="text-2xl font-bold text-white">智能规则创建向导</h2>
                <button onclick="closeModal('workflow-wizard-modal')" class="text-gray-400 hover:text-white">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="flex-1 p-6">
                <p class="text-gray-300">这是智能向导的内容区域。</p>
            </div>
        </div>
    </div>

    <script>
        function openModal(modalId) {
            console.log(`Opening modal: ${modalId}`);
            const modal = document.getElementById(modalId);
            if (!modal) {
                console.error(`Modal with id '${modalId}' not found`);
                return;
            }
            
            console.log(`Modal found, showing: ${modalId}`);
            
            // Show the modal
            modal.classList.remove('hidden');
            
            // Force a reflow to ensure the modal is visible before applying transitions
            modal.offsetHeight;
            
            // Apply the show animation
            setTimeout(() => {
                const modalContent = modal.querySelector('.modal-content');
                if (modalContent) {
                    modalContent.classList.remove('scale-95');
                    modalContent.classList.add('scale-100');
                }
                modal.classList.remove('opacity-0');
                modal.classList.add('opacity-100');
                console.log(`Modal animation applied: ${modalId}`);
            }, 10);
        }

        function closeModal(modalId) {
            console.log(`Closing modal: ${modalId}`);
            const modal = document.getElementById(modalId);
            if (!modal) {
                console.error(`Modal with id '${modalId}' not found`);
                return;
            }
            
            const modalContent = modal.querySelector('.modal-content');
            if (modalContent) {
                modalContent.classList.remove('scale-100');
                modalContent.classList.add('scale-95');
            }
            modal.classList.remove('opacity-100');
            modal.classList.add('opacity-0');
            
            setTimeout(() => {
                modal.classList.add('hidden');
                console.log(`Modal hidden: ${modalId}`);
            }, 300);
        }
    </script>
</body>
</html>
