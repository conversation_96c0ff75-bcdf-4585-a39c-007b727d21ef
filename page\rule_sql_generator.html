<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>规则SQL生成器</title>
    
    <!-- CSS 文件 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/scroller/2.0.5/css/scroller.bootstrap5.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    
    <!-- JavaScript 文件 -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/scroller/2.0.5/js/dataTables.scroller.min.js"></script>
    
    <style>
        :root {
            --primary-color: #0078D4;
            --bg-color: #f6f8fa;
            --card-bg: #ffffff;
            --text-primary: #0f172a;
            --text-secondary: #64748b;
        }
        
        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-primary);
            padding: 20px;
        }
        
        .container-fluid {
            max-width: 1800px;
        }
        
        .card {
            background-color: var(--card-bg);
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            margin-bottom: 20px;
        }
        
        .card-header {
            background-color: var(--primary-color);
            color: white;
            border-radius: 12px 12px 0 0 !important;
            padding: 15px 20px;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(0, 120, 212, 0.25);
        }
        
        .dataTables_wrapper .dataTables_paginate .paginate_button.current {
            background-color: var(--primary-color) !important;
            color: white !important;
            border: none;
        }
        
        #generatedSQL {
            background: #f8f9fa; 
            padding: 10px; 
            border-radius: 4px;
            min-height: 4px;
            max-height: 350px;
            height: auto;
            overflow-y: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
            border: 1px solid #ddd;
            margin-bottom: 15px;
        }
        
        /* 表格布局控制 */
        #rulesTable {
            width: 100%;
            table-layout: fixed;
            border-collapse: collapse;
        }
        
        /* 单元格内容样式 */
        #rulesTable td {
            word-wrap: break-word;
            white-space: normal;
            vertical-align: top;
            max-height: 150px;
            overflow-y: auto;
            padding: 8px;
        }
        
        /* 设置各列宽度 */
        #rulesTable th:nth-child(1), #rulesTable td:nth-child(1) { width: 3%; } /* 复选框 */
        #rulesTable th:nth-child(2), #rulesTable td:nth-child(2) { width: 4%; } /* 序号 */
        #rulesTable th:nth-child(3), #rulesTable td:nth-child(3) { width: 10%; } /* 行为认定 */
        #rulesTable th:nth-child(4), #rulesTable td:nth-child(4) { width: 12%; } /* 适用范围 */
        #rulesTable th:nth-child(5), #rulesTable td:nth-child(5) { width: 15%; } /* 规则名称 */
        #rulesTable th:nth-child(6), #rulesTable td:nth-child(6) { width: 8%; } /* 城市 */
        #rulesTable th:nth-child(7), #rulesTable td:nth-child(7) { width: 10%; } /* 规则来源 */
        #rulesTable th:nth-child(8), #rulesTable td:nth-child(8) { width: 6%; } /* 用途 */
        #rulesTable th:nth-child(9), #rulesTable td:nth-child(9) { width: 10%; } /* SQL状态 */
        #rulesTable th:nth-child(10), #rulesTable td:nth-child(10) { width: 32%; } /* 规则内涵 */
        
        /* 长文本单元格样式 */
        .long-text-cell {
            max-height: 120px;
            overflow-y: auto;
            line-height: 1.4;
        }
        
        /* 滚动条样式优化 */
        .long-text-cell::-webkit-scrollbar {
            width: 6px;
        }
        
        .long-text-cell::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        
        .long-text-cell::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 3px;
        }
        
        /* DataTables优化 */
        .dataTables_wrapper .dataTables_scroll {
            overflow: visible;
        }
        
        /* 修复DataTables边框对齐问题 */
        .dataTables_wrapper .dataTables_scrollHead table,
        .dataTables_wrapper .dataTables_scrollBody table {
            border-collapse: collapse;
            border-spacing: 0;
            width: 100% !important;
        }
        
        .dataTables_wrapper .dataTables_scrollHead th,
        .dataTables_wrapper .dataTables_scrollBody td {
            box-sizing: border-box;
        }
        
        /* 隐藏不可排序列的排序图标 */
        .dataTables_wrapper .dataTables_scrollHead th.sorting_disabled,
        .dataTables_wrapper .dataTables_scrollHead th.sorting_disabled::before,
        .dataTables_wrapper .dataTables_scrollHead th.sorting_disabled::after {
            background-image: none !important;
        }
        
        /* 强制隐藏复选框列和序号列的排序图标 */
        #rulesTable th:first-child,
        #rulesTable th:nth-child(2),
        #rulesTable th:nth-child(9) {
            background-image: none !important;
        }
        
        #rulesTable th:first-child::before,
        #rulesTable th:first-child::after,
        #rulesTable th:nth-child(2)::before,
        #rulesTable th:nth-child(2)::after,
        #rulesTable th:nth-child(9)::before,
        #rulesTable th:nth-child(9)::after {
            display: none !important;
        }
        
        /* 使用no-sort类隐藏排序图标 */
        .no-sort {
            background-image: none !important;
        }
        
        .no-sort::before,
        .no-sort::after {
            display: none !important;
        }

        /* 表格样式优化 */
        #rulesTable {
            font-size: 14px;
            border-collapse: collapse;
            width: 100%;
            table-layout: fixed;
            border-spacing: 0;
        }

        #rulesTable th {
            font-size: 13px;
            font-weight: 600;
            padding: 12px 10px;
            vertical-align: middle;
            background: linear-gradient(135deg, #343a40 0%, #495057 100%);
            color: white;
            border: 1px solid #dee2e6;
            text-align: center;
            white-space: nowrap;
            box-sizing: border-box;
        }

        #rulesTable td {
            padding: 10px 10px;
            vertical-align: middle;
            border: 1px solid #dee2e6;
            background: white;
            word-wrap: break-word;
            box-sizing: border-box;
        }

        #rulesTable tbody tr:hover {
            background-color: #f8f9fa;
        }

        #rulesTable tbody tr:nth-child(even) {
            background-color: #fdfdfd;
        }

        #rulesTable tbody tr:nth-child(even):hover {
            background-color: #f1f3f4;
        }

        /* 文本截断样式 */
        .text-truncate {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            cursor: help;
            max-width: 100%;
            display: block;
        }

        /* 用途标签样式 */
        .badge {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* 表格容器样式 */
        .table-responsive {
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        /* 复选框样式 */
        .rule-checkbox {
            transform: scale(1.1);
            cursor: pointer;
        }
    </style>
</head>
<body>
<div class="container-fluid">
    <nav class="mb-4">
        <a href="/" class="text-decoration-none">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                <path d="M9.78 12.78a.75.75 0 01-1.06 0L4.47 8.53a.75.75 0 010-1.06l4.25-4.25a.75.75 0 011.06 1.06L6.06 8l3.72 3.72a.75.75 0 010 1.06z"/>
            </svg>
            返回主页
        </a>
    </nav>

    <!-- 规则检索区域 -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">规则检索</h5>
        </div>
        <div class="card-body">
            <div class="row g-2 mb-3">

                <div class="col-md-1">
                    <label class="form-label">城市</label>
                    <select class="form-select" id="cityType">
                        <option value="">全部</option>
                    </select>
                </div>

                <div class="col-md-2">
                    <label class="form-label">规则名称</label>
                    <input type="text" class="form-control" id="ruleName">
                </div>
                <div class="col-md-2">
                    <label class="form-label">类型</label>
                    <select class="form-select" id="type">
                        <option value="">全部</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">规则类型</label>
                    <select class="form-select" id="ruletype">
                        <option value="">全部</option>
                    </select>
                </div>

                <div class="col-md-2">
                    <label class="form-label">规则来源</label>
                    <select class="form-select" id="ruleSource">
                        <option value="">全部</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">行为认定</label>
                    <select class="form-select" id="behaviorType">
                        <option value="">全部</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button class="btn btn-primary w-100" onclick="searchRules()">搜索</button>
                </div>
            </div>
        </div>
    </div>

    </div>

    <!-- SQL生成区域 -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">SQL生成器</h5>
        </div>
        <div class="card-body">
            <pre id="generatedSQL"></pre>
            <div class="row mb-3">
                <div class="col-md-3">
                    <label for="databaseSelect" class="form-label">选择数据库:</label>
                    <select class="form-select" id="databaseSelect" onchange="onDatabaseChange()">
                        <option value="pg" selected>PostgreSQL</option>
                        <option value="oracle">Oracle</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="hostInput" class="form-label">数据库主机IP:</label>
                    <input type="text" class="form-control" id="hostInput" placeholder="输入IP地址" onchange="loadDatabaseSchemas()">
                    <small class="form-text text-muted">留空使用默认主机</small>
                </div>
                <div class="col-md-3">
                    <label for="schemaSelect" class="form-label">选择Schema:</label>
                    <select class="form-select" id="schemaSelect">
                        <option value="">请先输入主机IP</option>
                    </select>
                </div>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-primary" onclick="copySQL()">
                    <i class="bi bi-clipboard"></i> 复制SQL
                </button>
                <button class="btn btn-success" onclick="executeSQL()">
                    <i class="bi bi-play"></i> 执行查询
                </button>
            </div>
            <!-- 执行结果显示区域 -->
            <div id="sqlResult" class="mt-3" style="display: none;">
                <h6>执行结果:</h6>
                <div id="resultContent"></div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 规则列表 -->
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <h5 class="mb-0">规则列表(SQL生成不完整)</h5>
                            <span class="ms-2 text-white">
                                (共 <span id="ruleCount">0</span> 条)
                            </span>
                        </div>
                        <div>
                            <div class="btn-group me-2">




                                <select class="form-select me-2" id="ruleType">
                                    <option value="name">项目名称</option>
                                    <option value="code">项目编码</option>
                                    <option value="national_code">国家编码</option>
                                    <option value="national_bureau">国家局</option>
                                </select>
                                <select class="form-select me-2" id="dbType">
                                    <option value="pg">PostgreSQL</option>
                                    <option value="oracle">Oracle</option>
                                </select>
                            </div>
                            <button class="btn btn-light" onclick="generateSelectedSQL()">
                                <i class="bi bi-code-square"></i> 生成选中SQL
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <style>
                        /* 表格布局控制 */
                        #rulesTable {
                            width: 100%;
                            table-layout: fixed;
                            border-collapse: collapse;
                        }
                        
                        /* 单元格内容样式 */
                        #rulesTable td {
                            word-wrap: break-word;
                            white-space: normal;
                            vertical-align: top;
                            max-height: 150px;
                            overflow-y: auto;
                            padding: 8px;
                        }
                        
                        /* 设置各列宽度 */
                        #rulesTable th:nth-child(1), #rulesTable td:nth-child(1) { width: 3%; }
                        #rulesTable th:nth-child(2), #rulesTable td:nth-child(2) { width: 4%; } /* 序号 */
                        #rulesTable th:nth-child(3), #rulesTable td:nth-child(3) { width: 8%; } /* 行为认定 */
                        #rulesTable th:nth-child(4), #rulesTable td:nth-child(4) { width: 12%; } /* 适用范围 */
                        #rulesTable th:nth-child(5), #rulesTable td:nth-child(5) { width: 15%; } /* 规则名称 */
                        #rulesTable th:nth-child(6), #rulesTable td:nth-child(6) { width: 6%; } /* 城市 */
                        #rulesTable th:nth-child(7), #rulesTable td:nth-child(7) { width: 9%; } /* 规则来源 */
                        #rulesTable th:nth-child(8), #rulesTable td:nth-child(8) { width: 6%; } /* 用途 */
                        #rulesTable th:nth-child(9), #rulesTable td:nth-child(9) { width: 10%; } /* SQL状态 */
                        #rulesTable th:nth-child(10), #rulesTable td:nth-child(10) { width: 34%; } /* 规则内涵 */
                        
                        /* 长文本单元格样式 */
                        .long-text-cell {
                            max-height: 120px;
                            overflow-y: auto;
                            line-height: 1.4;
                        }
                        
                        /* 滚动条样式优化 */
                        .long-text-cell::-webkit-scrollbar {
                            width: 6px;
                        }
                        
                        .long-text-cell::-webkit-scrollbar-track {
                            background: #f1f1f1;
                        }
                        
                        .long-text-cell::-webkit-scrollbar-thumb {
                            background: #888;
                            border-radius: 3px;
                        }
                        
                        /* DataTables优化 */
                        .dataTables_wrapper .dataTables_scroll {
                            overflow: visible;
                        }
                    </style>

                    <table id="rulesTable" class="table table-striped table-bordered">
                        <thead>
                            <tr>
                                <th width="40">
                                    <input type="checkbox" id="selectAll" onclick="toggleSelectAll()">
                                </th>
                                <th>序号</th>
                                <th>行为认定</th>
                                <th>适用范围</th>
                                <th>规则名称</th>
                                <th>城市</th>
                                <th>规则来源</th>
                                <th>用途</th>
                                <th>SQL状态</th>
                                <th>规则内涵</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

<script>
// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 为搜索输入框添加回车键事件监听
    document.getElementById('ruleName').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            searchRules();
        }
    });
    
    // 初始化其他功能
    initRulesTable();
    loadBehaviorTypes();
    loadCityTypes();
    loadRuleSources();
    loadRules();
    loadTypeTypes();
    loadRuleTypeTypes();
    onDatabaseChange(); // 初始化数据库选择
});

// 初始化 DataTable
let rulesTable;

function initRulesTable() {
    if ($.fn.DataTable.isDataTable('#rulesTable')) {
        rulesTable.destroy();
    }

    rulesTable = $('#rulesTable').DataTable({
        serverSide: false,
        deferRender: true,
        scrollY: '60vh',
        scroller: true,
        scrollCollapse: true,
        pageLength: 25,
        dom: '<"row"<"col-sm-12"tr>><"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
        //language: {
        //    url: '/static/chinese.json'
        //}
    });
}

// 加载行为认定选项
function loadBehaviorTypes() {
    fetch('/api/behavior_types')
        .then(response => response.json())
        .then(data => {
            if (data.success && Array.isArray(data.types)) {
                const select = document.getElementById('behaviorType');
                select.innerHTML = '<option value="">全部</option>';
                data.types.forEach(type => {
                    select.innerHTML += `<option value="${type}">${type}</option>`;
                });
            } else {
                console.error('加载行为认定失败:', data);
                showToast('加载行为认定失败：' + (data.error || '数据格式错误'), 'error');
            }
        })
        .catch(error => {
            console.error('加载失败:', error);
            showToast('加载行为认定失败', 'error');
        });
}

// 修复加载规则函数
function loadRules() {
    const params = new URLSearchParams({
        behavior_type: document.getElementById('behaviorType').value,
        city: document.getElementById('cityType').value,
        rule_source: document.getElementById('ruleSource').value,
        rule_name: document.getElementById('ruleName').value,
        type: document.getElementById('type').value,
        rule_type: document.getElementById('ruletype').value,
        visit_type: '', // 传递空值，让后端返回所有就诊类型的规则
    });

    console.log('开始加载规则，查询参数:', params.toString());
    showLoading();

    fetch(`/api/rules/search?${params}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`API响应错误: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('规则搜索API返回数据结构:', Object.keys(data));
            
            if (data.success) {
                const ruleCount = document.getElementById('ruleCount');
                
                if (!data.rules) {
                    console.error('API返回数据中缺少rules字段');
                    showToast('API返回数据格式错误：缺少rules字段', 'error');
                    return;
                }
                
                console.log(`获取到${data.rules.length}条规则数据`);
                ruleCount.textContent = data.rules.length;
                
                if (data.rules.length === 0) {
                    if ($.fn.DataTable.isDataTable('#rulesTable')) {
                        $('#rulesTable').DataTable().clear().draw();
                    }
                    showToast('暂无数据', 'info');
                    return;
                }
                
                try {
                    // 销毁已存在的DataTable实例
                    if ($.fn.DataTable.isDataTable('#rulesTable')) {
                        $('#rulesTable').DataTable().destroy();
                    }
                    
                    // 清空表格内容，确保干净的状态
                    $('#rulesTable tbody').empty();
                    
                    console.log('规则数据示例:', data.rules[0]);
                    
                    // 使用DataTables的正确初始化方式
                    $('#rulesTable').DataTable({
                        data: data.rules,
                        columns: [
                            {
                                // 复选框列
                                data: null,
                                orderable: false,
                                searchable: false,
                                width: "40px",
                                className: "no-sort",
                                render: function (data, type, row, meta) {
                                    const ruleId = row.id || row.ID || row.规则ID || meta.row;
                                    return `<input type="checkbox" class="rule-checkbox" value="${ruleId}" onchange="handleCheckboxChange()">`;
                                }
                            },
                            {
                                // 序号列
                                data: null,
                                orderable: false,
                                searchable: false,
                                width: "50px",
                                className: "no-sort",
                                render: function (data, type, row, meta) {
                                    return meta.row + 1;
                                }
                            },
                            {
                                data: '行为认定',
                                defaultContent: '',
                                width: '10%',
                                render: function(data, type, row) {
                                    if (type === 'display' && data && data.length > 20) {
                                        return `<div class="text-truncate" title="${data}">${data}</div>`;
                                    }
                                    return data || '';
                                }
                            },
                            {
                                data: '适用范围',
                                defaultContent: '',
                                width: '12%',
                                render: function(data, type, row) {
                                    if (type === 'display' && data && data.length > 30) {
                                        return `<div class="text-truncate" title="${data}">${data}</div>`;
                                    }
                                    return data || '';
                                }
                            },
                            {
                                data: '规则名称',
                                defaultContent: '',
                                width: '15%',
                                render: function(data, type, row) {
                                    if (type === 'display' && data && data.length > 25) {
                                        return `<div class="text-truncate" title="${data}">${data}</div>`;
                                    }
                                    return data || '';
                                }
                            },
                            {
                                data: '城市',
                                defaultContent: '',
                                width: '8%',
                                render: function(data, type, row) {
                                    return data || '';
                                }
                            },
                            {
                                data: '规则来源',
                                defaultContent: '',
                                width: '10%',
                                render: function(data, type, row) {
                                    if (type === 'display' && data && data.length > 15) {
                                        return `<div class="text-truncate" title="${data}">${data}</div>`;
                                    }
                                    return data || '';
                                }
                            },
                            {
                                data: '用途',
                                defaultContent: '',
                                width: '6%',
                                render: function(data, type, row) {
                                    const purpose = data || '';
                                    if (purpose) {
                                        const badgeClass = purpose.includes('门诊') ? 'bg-info' :
                                                          purpose.includes('住院') ? 'bg-warning' : 'bg-secondary';
                                        return `<span class="badge ${badgeClass}">${purpose}</span>`;
                                    }
                                    return '<span class="badge bg-light text-dark">未指定</span>';
                                }
                            },
                            {
                                // SQL状态列
                                data: null,
                                orderable: false,
                                searchable: false,
                                width: "100px",
                                className: "no-sort",
                                render: function(data, type, row) {
                                    return getSQLStatus(row);
                                }
                            },
                            {
                                data: '规则内涵',
                                defaultContent: '',
                                width: '32%',
                                render: function(data, type, row) {
                                    if (type === 'display' && data && data.length > 80) {
                                        return `<div class="text-truncate" title="${data}">${data}</div>`;
                                    }
                                    return data || '';
                                }
                            }
                        ],
                        deferRender: true,
                        scrollY: '60vh',
                        scroller: true,
                        scrollCollapse: true,
                        autoWidth: false, // 禁用自动宽度
                        pageLength: 25,
                        columnDefs: [
                            { targets: 0, width: '40px', className: 'text-center no-sort', orderable: false }, // 复选框
                            { targets: 1, width: '50px', className: 'text-center no-sort', orderable: false }, // 序号
                            { targets: 2, width: '10%', className: 'text-center' }, // 行为认定
                            { targets: 3, width: '12%' }, // 适用范围
                            { targets: 4, width: '15%' }, // 规则名称
                            { targets: 5, width: '8%', className: 'text-center' }, // 城市
                            { targets: 6, width: '10%', className: 'text-center' }, // 规则来源
                            { targets: 7, width: '6%', className: 'text-center' }, // 用途
                            { targets: 8, width: '100px', className: 'text-center no-sort', orderable: false }, // SQL状态
                            { targets: 9, width: '32%' } // 规则内涵
                        ],
                        //language: {
                        //    url: '/static/chinese.json'
                        //}
                    });
                    
                    console.log('表格渲染完成');
                    document.getElementById('selectAll').checked = false;
                    
                } catch (err) {
                    console.error('处理规则数据时出错:', err);
                    showToast('处理规则数据时出错: ' + err.message, 'error');
                }
            } else {
                console.error('加载规则列表失败:', data.error || '未知错误');
                showToast('加载规则列表失败：' + (data.error || '未知错误'), 'error');
            }
        })
        .catch(error => {
            console.error('加载规则失败:', error);
            showToast('加载规则失败：' + error.message, 'error');
        })
        .finally(() => {
            hideLoading();
            console.log('规则加载流程结束');
        });
}

// 加载城市类型选项
function loadCityTypes() {
    fetch('/api/city_types')
        .then(response => response.json())
        .then(data => {
            if (data.success && Array.isArray(data.types)) {
                const select = document.getElementById('cityType');
                select.innerHTML = '<option value="">全部</option>';
                data.types.forEach(type => {
                    select.innerHTML += `<option value="${type}">${type}</option>`;
                });
            } else {
                console.error('加载城市失败:', data);
                showToast('加载城市失败：' + (data.error || '数据格式错误'), 'error');
            }
        })
        .catch(error => {
            console.error('加载失败:', error);
            showToast('加载城市失败', 'error');
        });
}

// 修复加载规则来源选项 - 处理API返回的types字段
function loadRuleSources() {
    console.log('开始加载规则来源...');
    
    // 显示加载状态
    const select = document.getElementById('ruleSource');
    select.innerHTML = '<option value="">加载中...</option>';
    select.disabled = true;
    
    fetch('/api/rule_sources')
        .then(response => {
            console.log('规则来源API响应状态:', response.status);
            if (!response.ok) {
                throw new Error(`API响应错误: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('规则来源API返回数据:', data);
            
            // 重置选择框
            select.innerHTML = '<option value="">全部</option>';
            select.disabled = false;
            
            // 检查data.types存在(API返回types而不是sources)
            const sourceArray = data.sources || data.types || [];
            
            if (Array.isArray(sourceArray) && sourceArray.length > 0) {
                // 正常加载数据
                sourceArray.forEach(source => {
                    if (source) { // 确保数据有效
                        select.innerHTML += `<option value="${source}">${source}</option>`;
                    }
                });
                console.log(`成功加载 ${sourceArray.length} 个规则来源`);
            } else if (data.success && !Array.isArray(sourceArray)) {
                // 数据可能是对象格式
                console.warn('尝试处理非数组格式数据');
                // 尝试从响应中找到可能的数据源
                const possibleDataSources = ['sources', 'types', 'data', 'items', 'options'];
                
                for (const field of possibleDataSources) {
                    if (data[field] && Array.isArray(data[field])) {
                        data[field].forEach(item => {
                            if (item) {
                                select.innerHTML += `<option value="${item}">${item}</option>`;
                            }
                        });
                        console.log(`使用${field}字段加载了${data[field].length}个规则来源`);
                        return;
                    }
                }
                
                // 如果仍未找到数据，尝试JSON.stringify查看完整数据结构
                console.warn('无法识别的数据格式:', JSON.stringify(data));
                showToast('规则来源数据格式不支持，请检查控制台日志', 'warning');
            } else {
                console.error('加载规则来源失败:', data);
                showToast('加载规则来源失败：无可用数据', 'error');
            }
        })
        .catch(error => {
            console.error('加载规则来源失败:', error);
            select.innerHTML = '<option value="">加载失败</option>';
            select.disabled = false;
            showToast('加载规则来源失败: ' + error.message, 'error');
            
            // 紧急方案：提供基本选项
            setTimeout(() => {
                if (select.options.length <= 1) {
                    select.innerHTML = '<option value="">全部</option>';
                    ['内部规则', '外部规则'].forEach(source => {
                        select.innerHTML += `<option value="${source}">${source}</option>`;
                    });
                    console.log('已加载备用规则来源选项');
                }
            }, 500);
        });
}

// 添加加载指示器
function showLoading() {
    const loadingDiv = document.createElement('div');
    loadingDiv.id = 'loadingIndicator';
    loadingDiv.className = 'position-fixed top-50 start-50 translate-middle';
    loadingDiv.innerHTML = `
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
        </div>
    `;
    document.body.appendChild(loadingDiv);
}

function hideLoading() {
    const loadingDiv = document.getElementById('loadingIndicator');
    if (loadingDiv) {
        loadingDiv.remove();
    }
}

// 显示提示消息
function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toastContainer') || createToastContainer();
    
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type}`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');
    
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;
    
    toastContainer.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast, { autohide: true, delay: 3000 });
    bsToast.show();
    
    toast.addEventListener('hidden.bs.toast', function() {
        toast.remove();
    });
}

function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toastContainer';
    container.className = 'toast-container position-fixed top-0 end-0 p-3';
    container.style.zIndex = '1100';
    document.body.appendChild(container);
    return container;
}

// 处理搜索值的函数，支持特殊字符和通配符
function processSearchValue(value) {
    if (!value) return '';
    
    // 转义正则表达式中的特殊字符，但保留%
    let escaped = value.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    
    // 将%替换为.*（正则表达式中的任意字符匹配）
    escaped = escaped.replace(/%/g, '.*');
    
    return escaped;
}

// 修改搜索规则函数
function searchRules() {
    // 获取搜索条件并处理
    const behaviorType = document.getElementById('behaviorType').value;
    const cityType = document.getElementById('cityType').value;
    const ruleSource = document.getElementById('ruleSource').value;
    const ruleName = document.getElementById('ruleName').value;
    const type = document.getElementById('type').value;
    const ruleType = document.getElementById('ruletype').value;
    // 注意：已移除visitType，因为现在支持智能判断就诊类型
    // 加载规则
    loadRules();
}

function toggleSelectAll() {
    const checkAll = document.getElementById('selectAll').checked;
    const checkboxes = document.getElementsByClassName('rule-checkbox');
    for (let i = 0; i < checkboxes.length; i++) {
        checkboxes[i].checked = checkAll;
    }
    handleCheckboxChange();
}

function handleCheckboxChange() {
    const checkboxes = document.getElementsByClassName('rule-checkbox');
    let checkedCount = 0;
    for (let i = 0; i < checkboxes.length; i++) {
        if (checkboxes[i].checked) {
            checkedCount++;
        }
    }
    document.getElementById('selectAll').checked = (checkedCount > 0 && checkedCount === checkboxes.length);
}

function getSelectedRuleIds() {
    const checkboxes = document.getElementsByClassName('rule-checkbox');
    const selectedIds = [];
    for (let i = 0; i < checkboxes.length; i++) {
        if (checkboxes[i].checked) {
            selectedIds.push(checkboxes[i].value);
        }
    }
    return selectedIds;
}

function generateSelectedSQL() {
    // 获取选中的规则
    const selectedRules = getSelectedRuleIds();
    if (selectedRules.length === 0) {
        showToast('请至少选择一条规则', 'error');
        return;
    }

    // 获取选中的数据库类型和规则类别
    const selectedDbType = document.getElementById('dbType').value;
    const selectedRuleType = document.getElementById('ruleType').value;

    if (!selectedRuleType) {
        showToast('请选择规则类别', 'warning');
        return;
    }

    // 获取选中规则的详细信息，包括用途字段
    const selectedRuleData = getSelectedRuleData();
    if (!selectedRuleData || selectedRuleData.length === 0) {
        showToast('无法获取选中规则的详细信息', 'error');
        return;
    }

    // 按用途分组规则
    const rulesByPurpose = groupRulesByPurpose(selectedRuleData);

    // 生成所有需要的SQL
    const sqlGenerationTasks = [];

    // 为每个用途类型生成对应的SQL
    for (const [purpose, rules] of Object.entries(rulesByPurpose)) {
        const visitTypes = getVisitTypesForPurpose(purpose);

        for (const visitType of visitTypes) {
            const templatePath = getTemplatePath(selectedDbType, selectedRuleType, visitType);

            sqlGenerationTasks.push({
                rule_ids: rules.map(rule => rule.id || rule.ID || rule.对照id),
                template_path: templatePath,
                visit_type: visitType === 'outpatient' ? '门诊' : '住院',
                selected_rule_type: selectedRuleType,
                selected_db_type: selectedDbType,
                purpose: purpose,
                rule_count: rules.length
            });
        }
    }

    if (sqlGenerationTasks.length === 0) {
        showToast('没有找到可生成SQL的规则', 'warning');
        return;
    }

    showLoading();

    // 批量生成SQL
    generateBatchSQL(sqlGenerationTasks);
}

// 获取选中规则的详细数据
function getSelectedRuleData() {
    const selectedIds = getSelectedRuleIds();
    console.log('选中的ID列表:', selectedIds);

    const table = $('#rulesTable').DataTable();
    const allData = table.data().toArray();
    console.log('表格数据示例:', allData.length > 0 ? allData[0] : '无数据');

    // 尝试多种ID字段匹配
    const selectedData = allData.filter((rule, index) => {
        const ruleId = rule.id || rule.ID || rule.规则ID || index.toString();
        return selectedIds.includes(ruleId.toString());
    });

    console.log('匹配到的规则数据:', selectedData.length);
    return selectedData;
}

// 按用途分组规则
function groupRulesByPurpose(rules) {
    const groups = {};

    rules.forEach(rule => {
        const purpose = rule.用途 || rule.purpose || '';

        if (!groups[purpose]) {
            groups[purpose] = [];
        }
        groups[purpose].push(rule);
    });

    return groups;
}

// 根据用途确定需要生成的就诊类型
function getVisitTypesForPurpose(purpose) {
    if (!purpose || purpose.trim() === '') {
        // 用途为空，生成门诊和住院两种
        return ['outpatient', 'inpatient'];
    }

    const purposeLower = purpose.toLowerCase().trim();

    if (purposeLower.includes('门诊') || purposeLower === '门诊') {
        return ['outpatient'];
    } else if (purposeLower.includes('住院') || purposeLower === '住院') {
        return ['inpatient'];
    } else {
        // 其他情况，生成门诊和住院两种
        return ['outpatient', 'inpatient'];
    }
}

// 获取模板路径
function getTemplatePath(dbType, ruleType, visitType) {
    return `rule_${dbType}_${ruleType}_${visitType}`;
}

// 批量生成SQL（使用现有API逐个调用）
async function generateBatchSQL(tasks) {
    console.log('批量生成SQL任务:', tasks);

    const results = [];
    let completedTasks = 0;

    try {
        for (const task of tasks) {
            try {
                const response = await fetch('/api/generate_rule_sql', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(task)
                });

                const data = await response.json();
                results.push({
                    success: data.success,
                    sql: data.sql || '',
                    error: data.error || ''
                });

                completedTasks++;

                // 更新进度提示
                showToast(`正在生成SQL... (${completedTasks}/${tasks.length})`, 'info');

            } catch (error) {
                results.push({
                    success: false,
                    sql: '',
                    error: error.message
                });
                completedTasks++;
            }
        }

        hideLoading();
        displayBatchSQLResults(results, tasks);

    } catch (error) {
        hideLoading();
        console.error('批量生成SQL失败:', error);
        showToast('批量生成SQL失败: ' + error.message, 'error');
    }
}

// 显示批量SQL生成结果
function displayBatchSQLResults(results, tasks) {
    let combinedSQL = '';
    let successCount = 0;

    results.forEach((result, index) => {
        const task = tasks[index];
        if (result.success) {
            successCount++;
            combinedSQL += `-- ${task.purpose || '未指定用途'} - ${task.visit_type} (${task.rule_count}条规则)\n`;
            combinedSQL += result.sql + '\n\n';
        } else {
            combinedSQL += `-- 生成失败: ${task.purpose || '未指定用途'} - ${task.visit_type}\n`;
            combinedSQL += `-- 错误: ${result.error}\n\n`;
        }
    });

    document.getElementById('generatedSQL').textContent = combinedSQL;

    // 显示成功弹窗
    showConfirmDialog(
        '智能SQL生成完成',
        `已根据规则用途智能生成 ${successCount}/${tasks.length} 个SQL模板。`,
        '确定',
        () => {
            // 用户点击确定后刷新规则列表
            loadRules();
        }
    );
}

// 添加确认对话框函数
function showConfirmDialog(title, message, confirmText, onConfirm) {
    // 检查是否已存在对话框，如果存在则移除
    const existingModal = document.getElementById('confirmModal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // 创建对话框HTML
    const modalHtml = `
    <div class="modal fade" id="confirmModal" tabindex="-1" aria-labelledby="confirmModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="confirmModalLabel">${title}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    ${message}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" id="confirmBtn">${confirmText}</button>
                </div>
            </div>
        </div>
    </div>
    `;
    
    // 添加到文档
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    
    // 获取对话框元素
    const modalElement = document.getElementById('confirmModal');
    const modal = new bootstrap.Modal(modalElement);
    
    // 绑定确认按钮事件
    document.getElementById('confirmBtn').addEventListener('click', function() {
        modal.hide();
        if (typeof onConfirm === 'function') {
            onConfirm();
        }
    });
    
    // 显示对话框
    modal.show();
    
    // 对话框关闭后移除元素
    modalElement.addEventListener('hidden.bs.modal', function() {
        modalElement.remove();
    });
}

function generateTemplateSQL() {
    // 此处保留原有功能
    showToast('模板生成功能待实现', 'info');
}

function copySQL() {
    const sql = document.getElementById('generatedSQL').textContent;
    if (!sql) {
        showToast('没有可复制的SQL', 'warning');
        return;
    }
    
    navigator.clipboard.writeText(sql)
        .then(() => {
            showToast('SQL已复制到剪贴板', 'success');
        })
        .catch(err => {
            console.error('复制失败:', err);
            showToast('复制失败', 'error');
        });
}

// 数据库类型改变时的处理
function onDatabaseChange() {
    const schemaSelect = document.getElementById('schemaSelect');
    const hostInput = document.getElementById('hostInput');

    // 清空schema选择
    schemaSelect.innerHTML = '<option value="">请先输入主机IP</option>';

    // 清空主机IP输入
    hostInput.value = '';

    // 根据数据库类型设置默认提示
    const database = document.getElementById('databaseSelect').value;
    if (database === 'oracle') {
        hostInput.placeholder = '输入Oracle主机IP (默认: 127.0.0.1, 用户: datachange)';
    } else {
        hostInput.placeholder = '输入PostgreSQL主机IP (默认: *************, 用户: postgres)';
    }
}

// 加载数据库Schema列表
function loadDatabaseSchemas() {
    const database = document.getElementById('databaseSelect').value;
    const hostInput = document.getElementById('hostInput');
    const schemaSelect = document.getElementById('schemaSelect');

    const host = hostInput.value.trim();

    // 显示加载状态
    schemaSelect.innerHTML = '<option value="">加载中...</option>';
    schemaSelect.disabled = true;

    fetch('/api/database_schemas', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            database: database,
            host: host || 'default'  // 如果没有输入主机，使用默认
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            schemaSelect.innerHTML = '<option value="">默认Schema</option>';
            schemaSelect.disabled = false;

            const schemas = data.schemas || [];
            schemas.forEach(schema => {
                const option = document.createElement('option');
                option.value = schema;
                option.textContent = schema;
                schemaSelect.appendChild(option);
            });

            if (schemas.length === 0) {
                schemaSelect.innerHTML = '<option value="">无可用Schema</option>';
            }
        } else {
            schemaSelect.innerHTML = '<option value="">加载失败</option>';
            schemaSelect.disabled = false;
            showToast('加载Schema失败: ' + data.error, 'warning');
        }
    })
    .catch(error => {
        console.error('加载Schema失败:', error);
        schemaSelect.innerHTML = '<option value="">加载失败</option>';
        schemaSelect.disabled = false;
        showToast('加载Schema失败', 'error');
    });
}

function executeSQL() {
    const sql = document.getElementById('generatedSQL').textContent;
    const database = document.getElementById('databaseSelect').value;
    const host = document.getElementById('hostInput').value.trim();
    const schema = document.getElementById('schemaSelect').value;

    if (!sql) {
        showToast('没有可执行的SQL', 'warning');
        return;
    }

    // 显示执行结果区域
    const resultDiv = document.getElementById('sqlResult');
    const resultContent = document.getElementById('resultContent');
    resultDiv.style.display = 'block';
    resultContent.innerHTML = '<div class="text-center"><i class="bi bi-hourglass-split"></i> 正在执行SQL...</div>';

    showLoading();

    fetch('/api/rules/execute_sql', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            sql: sql,
            database: database,
            host: host || 'default',
            schema: schema
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showToast(`SQL执行成功！数据库: ${data.database}，返回 ${data.affected_rows} 条记录`, 'success');

            // 显示详细执行结果
            let resultHtml = `
                <div class="alert alert-success">
                    <strong>执行成功！</strong><br>
                    数据库: ${data.database}<br>
                    返回行数: ${data.affected_rows}
                </div>
            `;

            if (data.data && data.data.length > 0) {
                resultHtml += `
                    <div class="mt-3">
                        <h6 class="text-primary"><i class="bi bi-table"></i> 查询结果</h6>
                        <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                            <table class="table table-sm table-hover table-bordered">
                                <thead class="table-dark sticky-top">`;

                // 表头
                if (data.columns && data.columns.length > 0) {
                    resultHtml += '<tr>';
                    data.columns.forEach(col => {
                        resultHtml += `<th style="white-space: nowrap; font-size: 12px;">${col}</th>`;
                    });
                    resultHtml += '</tr>';
                }
                resultHtml += '</thead><tbody>';

                // 数据行（只显示前10行）
                data.data.slice(0, 10).forEach((row, index) => {
                    resultHtml += `<tr class="${index % 2 === 0 ? 'table-light' : ''}">`;
                    if (Array.isArray(row)) {
                        row.forEach(cell => {
                            let cellValue = cell !== null ? cell : '<span class="text-muted">NULL</span>';
                            // 限制单元格内容长度，避免过长
                            if (typeof cellValue === 'string' && cellValue.length > 50) {
                                cellValue = cellValue.substring(0, 50) + '...';
                            }
                            resultHtml += `<td style="font-size: 11px; max-width: 200px; word-wrap: break-word;">${cellValue}</td>`;
                        });
                    } else {
                        let cellValue = row !== null ? row : '<span class="text-muted">NULL</span>';
                        resultHtml += `<td style="font-size: 11px;">${cellValue}</td>`;
                    }
                    resultHtml += '</tr>';
                });
                resultHtml += `
                            </tbody>
                        </table>
                    </div>
                `;

                if (data.data.length >= 10) {
                    resultHtml += `
                        <div class="alert alert-info alert-sm mt-2">
                            <i class="bi bi-info-circle"></i>
                            <small>为提高查询速度，仅显示前10行数据。如需查看更多数据，请在业务数据库中执行完整SQL。</small>
                        </div>
                    `;
                }

                // 添加数据统计信息
                resultHtml += `
                    <div class="row mt-2">
                        <div class="col-md-6">
                            <small class="text-muted">
                                <i class="bi bi-bar-chart"></i>
                                列数: ${data.columns ? data.columns.length : 0} |
                                显示行数: ${Math.min(data.data.length, 10)}
                            </small>
                        </div>
                        <div class="col-md-6 text-end">
                            <small class="text-success">
                                <i class="bi bi-check-circle"></i>
                                SQL语法验证通过
                            </small>
                        </div>
                    </div>
                `;
            }

            resultContent.innerHTML = resultHtml;
        } else {
            showToast('SQL执行失败：' + data.error, 'error');
            resultContent.innerHTML = `
                <div class="alert alert-danger">
                    <strong>执行失败:</strong> ${data.error}
                </div>
            `;
        }
    })
    .catch(error => {
        hideLoading();
        console.error('SQL执行失败:', error);
        showToast('SQL执行失败', 'error');
        resultContent.innerHTML = `
            <div class="alert alert-danger">
                <strong>执行错误:</strong> ${error.message}
            </div>
        `;
    });
}

// 初始化类型和规则类型下拉框的加载函数（如无则补充）
function loadTypeTypes() {
    fetch('/api/type_types')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('type');
            if (data.success && Array.isArray(data.types) && select) {
                select.innerHTML = '<option value="">全部</option>';
                data.types.forEach(type => {
                    select.innerHTML += `<option value="${type}">${type}</option>`;
                });
                console.log('类型下拉框已绑定:', data.types);
            } else {
                console.error('类型下拉框数据异常:', data);
                select.innerHTML = '<option value="">全部</option>';
            }
        })
        .catch(error => {
            console.error('加载类型失败:', error);
            const select = document.getElementById('type');
            if (select) select.innerHTML = '<option value="">全部</option>';
        });
}

function loadRuleTypeTypes() {
    fetch('/api/rule_type_types')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('ruletype');
            if (data.success && Array.isArray(data.types) && select) {
                select.innerHTML = '<option value="">全部</option>';
                data.types.forEach(type => {
                    select.innerHTML += `<option value="${type}">${type}</option>`;
                });
                console.log('规则类型下拉框已绑定:', data.types);
            } else {
                console.error('规则类型下拉框数据异常:', data);
                select.innerHTML = '<option value="">全部</option>';
            }
        })
        .catch(error => {
            console.error('加载规则类型失败:', error);
            const select = document.getElementById('ruletype');
            if (select) select.innerHTML = '<option value="">全部</option>';
        });
}

// 获取SQL状态的函数
function getSQLStatus(row) {
    const ruleId = row.id || row.ID || row.规则ID;
    const purpose = row.用途 || '';

    // 如果没有规则ID，返回未知状态
    if (!ruleId) {
        return '<span class="badge bg-secondary">未知</span>';
    }

    // 异步检查SQL生成状态
    checkSQLStatusAsync(ruleId, purpose);

    // 先返回加载状态，后续通过异步更新
    return `<span class="badge bg-light text-dark" id="sql-status-${ruleId}">检查中...</span>`;
}

// 异步检查SQL状态
function checkSQLStatusAsync(ruleId, purpose) {
    fetch(`/api/check_sql_status/${ruleId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const statusElement = document.getElementById(`sql-status-${ruleId}`);
                if (statusElement) {
                    const status = determineSQLStatus(data.sql_records, purpose);
                    statusElement.outerHTML = status;
                }
            }
        })
        .catch(error => {
            console.error('检查SQL状态失败:', error);
            const statusElement = document.getElementById(`sql-status-${ruleId}`);
            if (statusElement) {
                statusElement.outerHTML = '<span class="badge bg-danger">检查失败</span>';
            }
        });
}

// 根据SQL记录和规则用途确定状态
function determineSQLStatus(sqlRecords, purpose) {
    const hasOutpatient = sqlRecords.some(record => record.visit_type === '门诊');
    const hasInpatient = sqlRecords.some(record => record.visit_type === '住院');

    // 如果没有任何SQL记录
    if (sqlRecords.length === 0) {
        return '<span class="badge bg-danger">未生成</span>';
    }

    // 根据规则用途判断完整性
    if (!purpose || purpose === '') {
        // 用途未指定，需要门诊和住院都有
        if (hasOutpatient && hasInpatient) {
            return '<span class="badge bg-success">完整</span>';
        } else if (hasOutpatient && !hasInpatient) {
            return '<span class="badge bg-warning text-dark">缺少住院SQL</span>';
        } else if (!hasOutpatient && hasInpatient) {
            return '<span class="badge bg-warning text-dark">缺少门诊SQL</span>';
        }
    } else if (purpose.includes('门诊') && purpose.includes('住院')) {
        // 同时适用门诊和住院
        if (hasOutpatient && hasInpatient) {
            return '<span class="badge bg-success">完整</span>';
        } else if (hasOutpatient && !hasInpatient) {
            return '<span class="badge bg-warning text-dark">缺少住院SQL</span>';
        } else if (!hasOutpatient && hasInpatient) {
            return '<span class="badge bg-warning text-dark">缺少门诊SQL</span>';
        }
    } else if (purpose.includes('门诊')) {
        // 仅适用门诊
        if (hasOutpatient) {
            return '<span class="badge bg-success">完整</span>';
        } else {
            return '<span class="badge bg-warning text-dark">缺少门诊SQL</span>';
        }
    } else if (purpose.includes('住院')) {
        // 仅适用住院
        if (hasInpatient) {
            return '<span class="badge bg-success">完整</span>';
        } else {
            return '<span class="badge bg-warning text-dark">缺少住院SQL</span>';
        }
    }

    // 默认情况
    return '<span class="badge bg-secondary">未知</span>';
}

</script>
</body>
</html>