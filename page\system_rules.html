<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>已保存的规则SQL</title>
    
    <!-- 修改为 Bootstrap 样式 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    
    <!-- JavaScript 文件 -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

    <style>
        :root {
            --primary-color: #0078D4;
            --bg-color: #f6f8fa;
            --card-bg: #ffffff;
            --text-primary: #0f172a;
            --text-secondary: #64748b;
        }
        
        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-primary);
            padding: 10px; /* 减少padding以获得更多空间 */
            font-size: 16px; /* 稍微减小字体 */
        }
        
        .container-fluid {
            max-width: 2400px; /* 增加最大宽度 */
            margin: 0 auto;
        }
        
        .card {
            background-color: var(--card-bg);
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            margin-bottom: 20px;
        }
        
        .card-header {
            background-color: var(--primary-color);
            color: white;
            border-radius: 12px 12px 0 0 !important;
            padding: 15px 20px;
        }
        
        .btn-group .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
        
        .btn-group .btn-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }
        
        .btn-group .btn-danger {
            background-color: #dc3545;
            border-color: #dc3545;
        }
        
        .btn-group .btn:hover {
            opacity: 0.9;
        }
        
        .btn-group .bi {
            font-size: 1rem;
        }
        
        /* 调整列宽度 */
        .table th.narrow-column,
        .table td.narrow-column {
            width: 90px;
            max-width: 90px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 16px;
        }

        .table th.medium-column,
        .table td.medium-column {
            width: 140px;
            max-width: 140px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 16px;
        }

        .table th.date-column,
        .table td.date-column {
            width: 160px;
            max-width: 160px;
            white-space: nowrap;
            font-size: 16px;
        }

        /* 规则名称列 */
        .table th.rule-name-column,
        .table td.rule-name-column {
            min-width: 200px;
            max-width: 300px;
            white-space: normal;
            word-break: break-word;
            font-size: 16px;
        }
        
        /* 增加规则内涵列的宽度 */
        .table th.rule-content,
        .table td.rule-content {
            min-width: 350px;  /* 适当减小以平衡整体布局 */
            max-width: none;
            white-space: normal;
            word-break: break-word;
            line-height: 1.3;
            padding: 6px 10px;
            font-size: 16px;
        }
        
        /* 操作列样式 */
        .table th.action-column,
        .table td.action-column {
            width: 100px;
            max-width: 100px;
            white-space: nowrap;
            font-size: 16px;
        }

        /* 添加表格容器样式以支持水平滚动 */
        .table-responsive {
            overflow-x: auto;
            margin: 0 -20px;  /* 增加负边距以获得更多空间 */
            padding: 0 20px;
        }

        /* 表格整体样式优化 */
        .table {
            font-size: 15px;
            margin-bottom: 0;
        }

        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
            border-bottom: 2px solid #dee2e6;
            padding: 8px 6px;
            vertical-align: middle;
        }

        .table td {
            padding: 6px 6px;
            vertical-align: middle;
        }

        /* 紧凑模式 */
        .table-sm th,
        .table-sm td {
            padding: 4px 6px;
        }

        .alert {
            max-width: 500px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        /* 隐藏checkbox列的排序图标 */
        .no-sort {
            background-image: none !important;
        }
        
        .no-sort::before,
        .no-sort::after {
            display: none !important;
        }
        
        /* 确保checkbox列不显示排序箭头 */
        table.dataTable thead .sorting,
        table.dataTable thead .sorting_asc,
        table.dataTable thead .sorting_desc {
            background-image: none !important;
        }
        
        /* 针对第一列（checkbox列）的特殊处理 */
        table.dataTable thead th:first-child {
            background-image: none !important;
            cursor: default !important;
        }
        
        table.dataTable thead th:first-child::before,
        table.dataTable thead th:first-child::after {
            display: none !important;
        }
        
        /* 更强力的checkbox列排序图标隐藏 */
        #rulesTable thead th:first-child {
            background-image: none !important;
            background: none !important;
            cursor: default !important;
        }
        
        #rulesTable thead th:first-child.sorting,
        #rulesTable thead th:first-child.sorting_asc,
        #rulesTable thead th:first-child.sorting_desc {
            background-image: none !important;
            background: none !important;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <nav>
            <a href="/" class="btn btn-link">
                <i class="bi bi-arrow-left"></i> 返回主页
            </a>
        </nav>

        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h3 class="mb-0">系统规则SQL</h3>
                    <div class="toolbar">
                        <span class="me-3">已选择 <span id="selectedCount" class="fw-bold">0</span> 条规则</span>
                        <button id="batchDeleteBtn" class="btn btn-danger me-2" onclick="batchDeleteSql()" disabled>
                            <i class="bi bi-trash"></i> 批量删除
                        </button>
                        <button id="importToPGBtn" class="btn btn-primary" onclick="importToPG()">
                            <i class="bi bi-download"></i> 导入需求到PG
                        </button>
                        <button id="importRuleToPGBtn" class="btn btn-primary" onclick="importRuleToPG()">
                            <i class="bi bi-download"></i> 导入SQL到PG
                        </button>
                        <button id="exportSQLBtn" onclick="exportSQLFile()" class="btn btn-success me-2">
                            <i class="bi bi-download"></i> 导出SQL文件
                        </button>
                        <button id="dbConfigBtn" class="btn btn-secondary me-2" onclick="showDbConfigModal()">
                            <i class="bi bi-gear"></i> 数据库设置
                        </button>
                        <button onclick="refreshList()" class="btn btn-light">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="card-body">
                <!-- 搜索区域 -->
                <div class="row mb-4">
                    <div class="col-md-2 mb-1">
                        <select class="form-select" id="visitType">
                            <option value="">选择就诊类型</option>
                            <option value="住院">住院</option>
                            <option value="门诊">门诊</option>
                        </select>
                    </div>
                    <div class="col-md-2 mb-2">
                        <select class="form-select" id="city">
                            <option value="">选择城市</option>
                        </select>
                    </div>

                    <div class="col-md-2 mb-1">
                        <select class="form-select" id="ruleType">
                            <option value="">选择规则类型</option>
                        </select>
                    </div>

                    <div class="col-md-2 mb-2">
                        <select class="form-select" id="ruleSource">
                            <option value="">选择规则来源</option>
                        </select>
                    </div>
                    <div class="col-md-2 mb-2">
                        <select class="form-select" id="hospitalFilter">
                            <option value="">全部医院</option>
                        </select>
                    </div>
                    <div class="col-md-2 mb-2">
                        <input type="text" class="form-control" id="ruleName" placeholder="规则名称">
                    </div>

                    <div class="col-md-2 mb-1">
                        <select class="form-select" id="type">
                            <option value="">选择类型</option>
                            <option value="定量">定量</option>
                            <option value="定性">定性</option>
                        </select>
                    </div>

                    <div class="col-md-3 mb-2">
                        <input type="text" class="form-control" id="ruleIdInput" placeholder="规则ID（支持多个，用;或|分隔）">
                    </div>

                    <div class="col-md-3 mb-2">
                        <button class="btn btn-primary me-2" onclick="searchRules()">搜索</button>
                        <button class="btn btn-secondary" onclick="resetFilters()">重置</button>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="table-responsive">
                    <table id="rulesTable" class="table table-striped table-bordered table-sm">
                        <thead>
                            <tr>
                                <th width="40">
                                    <input type="checkbox" id="selectAll" onclick="toggleSelectAll()">
                                </th>
                                <th class="narrow-column">对照ID</th>
                                <th class="narrow-column">规则ID</th>
                                <th class="rule-name-column">规则名称</th>
                                <th class="narrow-column">城市</th>
                                <th class="medium-column">规则来源</th>
                                <th class="narrow-column">就诊类型</th>
                                <th class="medium-column">规则类型</th>
                                <th class="narrow-column">类型</th>
                                <th class="medium-column">适用范围</th>
                                <th class="narrow-column">数据库类型</th>
                                <th class="rule-content">规则内涵</th>
                                <th class="medium-column">模板名称</th>
                                <th class="medium-column">未生成SQL</th>
                                <th class="date-column">创建时间</th>
                                <th class="action-column">操作</th>
                            </tr>
                        </thead>
                        <tbody id="rulesTableBody">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- SQL编辑弹窗 -->
    <div class="modal fade" id="sqlEditModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑SQL</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <textarea id="sqlEditor" class="form-control font-monospace" rows="15"></textarea>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveSqlChanges()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加模态框 -->
    <div class="modal fade" id="pgImportModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">导入配置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info" id="selectedRuleInfo">
                        <i class="bi bi-info-circle"></i>
                        <strong>当前选中规则：</strong><span id="currentRuleInfo">请选择规则</span>
                    </div>
                    <form id="pgImportForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">省份ID</label>
                                    <input type="text" class="form-control" name="province_id" value="32" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">省份名称</label>
                                    <input type="text" class="form-control" name="province_name" value="江苏" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">城市ID</label>
                                    <input type="text" class="form-control" name="city_id" value="3201" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">城市名称</label>
                                    <input type="text" class="form-control" name="city_name" value="南京" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">医院编码</label>
                                    <input type="text" class="form-control" name="hospital_code" value="H32010000001" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">医院名称</label>
                                    <input type="text" class="form-control" name="hospital_name" value="南京市第一人民医院" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">机构编码</label>
                                    <input type="text" class="form-control" name="org_code" value="ZQS_YY_ZQDXFSFLYY_7ZD" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">医保/医院</label>
                                    <select class="form-select" name="yb_or_yy" required>
                                        <option value="2">医院</option>
                                        <option value="1">医保</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <label class="form-label">选择城市</label>
                                    <select class="form-select" id="citySelector" onchange="onCityChange()">
                                        <option value="">请选择城市</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">&nbsp;</label>
                                    <div>
                                        <button type="button" class="btn btn-outline-secondary" onclick="autoFillConfig()">
                                            <i class="bi bi-magic"></i> 自动填充配置
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <small class="form-text text-muted">选择城市或点击按钮根据当前选中的规则城市自动填充配置信息</small>
                        </div>
                    </form>
                    
                    <!-- 配置预览 -->
                    <div class="mt-4">
                        <h6><i class="bi bi-eye"></i> 配置预览</h6>
                        <div class="card">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>省份信息：</strong>
                                        <div id="previewProvince">-</div>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>城市信息：</strong>
                                        <div id="previewCity">-</div>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-md-6">
                                        <strong>医院信息：</strong>
                                        <div id="previewHospital">-</div>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>机构信息：</strong>
                                        <div id="previewOrg">-</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="submitImport()">确认导入</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据库设置模态框 -->
    <div class="modal fade" id="dbConfigModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">PG数据库设置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="dbConfigForm">
                        <div class="mb-3">
                            <label class="form-label">主机</label>
                            <input type="text" class="form-control" name="host" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">端口</label>
                            <input type="text" class="form-control" name="port" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">数据库名</label>
                            <input type="text" class="form-control" name="dbname" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">用户名</label>
                            <input type="text" class="form-control" name="user" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">密码</label>
                            <input type="password" class="form-control" name="password" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Schema</label>
                            <input type="text" class="form-control" name="schema" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveDbConfig()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 在页面加载时初始化模态框对象
        let sqlEditModal;
        $(document).ready(function() {
            // 初始化模态框
            sqlEditModal = new bootstrap.Modal(document.getElementById('sqlEditModal'));

            // 完全重写表格初始化
            initializeRulesTable();

            // 初始化各个下拉框
            loadFilterOptions();

            // 加载医院列表
            loadHospitalOptions();

            // 检查URL参数，如果有ruleId参数则自动过滤
            checkUrlParameters();
        });

        let currentEditingId = null;
        let currentEditingVisitType = null;
        let hospitalAdoptedRuleIds = []; // 存储当前选中医院的已采用规则对照ID列表

        // 完全重写表格初始化
        function initializeRulesTable() {
            // 如果表格已经初始化，先销毁它
            if ($.fn.DataTable.isDataTable('#rulesTable')) {
                $('#rulesTable').DataTable().destroy();
            }
            
            // 重新初始化表格
            $('#rulesTable').DataTable({
                ajax: {
                    url: '/api/sql_history',
                    dataSrc: ''
                },
                scrollX: true,  // 启用水平滚动
                autoWidth: false,  // 禁用自动宽度
                pageLength: 25,  // 每页显示更多行
                lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
                columns: [
                    {
                        // 复选框列
                        data: null,
                        orderable: false,
                        searchable: false,
                        width: "40px",
                        className: "no-sort",
                        render: function (data, type, row) {
                            return `<input type="checkbox" class="rule-checkbox" value="${row.compare_id}" onchange="handleCheckboxChange()">`;
                        }
                    },
                    { data: 'compare_id', width: "80px"},      // 对照ID
                    { data: 'rule_id', width: "70px" },         // 规则ID
                    { data: 'rule_name', width: "250px" },       // 规则名称
                    { data: 'city', width: "80px" },            // 城市
                    { data: 'rule_source', width: "120px" },     // 规则来源
                    { data: 'visit_type', width: "80px" },      // 就诊类型
                    { data: 'rule_type', width: "120px" },       // 规则类型
                    { data: 'types', width: "80px" },           // 类型
                    { data: 'appl_scope', width: "120px" },         // 适用范围
                    { data: 'sql_type', width: "100px" },   // 选择数据库类型
                    { data: 'rule_content', width: "350px" },    // 规则内涵
                    { data: 'template_name', width: "150px" },   // 模板名称
                    {
                        // 未生成SQL列
                        data: 'missing_sql',
                        width: "120px",
                        render: function (data, type, row) {
                            if (data && data.trim() !== '') {
                                return `<span class="badge bg-warning text-dark">${data}</span>`;
                            }
                            return '<span class="badge bg-success">完整</span>';
                        }
                    },
                    { data: 'create_time', width: "150px" },     // 创建时间
                    
                    {
                        // 操作列
                        data: null,
                        orderable: false,
                        width: "100px",
                        render: function (data, type, row) {
                            return `<div class="btn-group">
                                <button class="btn btn-sm btn-primary" onclick="editSql(${row.compare_id}, '${row.visit_type}')">
                                    <i class="bi bi-pencil-square"></i>
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="deleteSql(${row.compare_id}, '${row.visit_type}')">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>`;
                        }
                    }
                ],
                language: {
                    "sProcessing": "处理中...",
                    "sLengthMenu": "显示 _MENU_ 条",
                    "sZeroRecords": "没有匹配结果",
                    "sInfo": "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
                    "sInfoEmpty": "显示第 0 至 0 项结果，共 0 项",
                    "sInfoFiltered": "(由 _MAX_ 项结果过滤)",
                    "sInfoPostFix": "",
                    "sSearch": "搜索:",
                    "sUrl": "",
                    "sEmptyTable": "表中数据为空",
                    "sLoadingRecords": "载入中...",
                    "sInfoThousands": ",",
                    "oPaginate": {
                        "sFirst": "首页",
                        "sPrevious": "上页",
                        "sNext": "下页",
                        "sLast": "末页"
                    }
                }
            });
            
            // 初始化后更新选中计数
            updateSelectedCount();
            
            // 确保checkbox列不显示排序图标
            setTimeout(function() {
                const firstHeader = $('#rulesTable thead th:first-child');
                firstHeader.removeClass('sorting sorting_asc sorting_desc');
                firstHeader.css('background-image', 'none');
                firstHeader.css('cursor', 'default');
            }, 100);
        }

        // 刷新列表函数
        function refreshList() {
            $('#rulesTable').DataTable().ajax.reload();
            document.getElementById('selectAll').checked = false;
            updateSelectedCount();
        }

        // 修改编辑SQL函数
        function editSql(ruleId, visitType) {
            currentEditingId = ruleId;
            currentEditingVisitType = visitType;
            
            // 先显示模态框
            sqlEditModal.show();
            
            // 显示加载状态
            $('#sqlEditor').val('加载中...');
            
            // 获取SQL内容
            fetch(`/api/sql_content/${ruleId}/${encodeURIComponent(visitType)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert('获取SQL内容失败：' + data.error);
                        return;
                    }
                    
                    // 解码HTML实体
                    const decodedSql = data.sql_content.replace(/&quot;/g, '"')
                                                   .replace(/&#039;/g, "'")
                                                   .replace(/&lt;/g, '<')
                                                   .replace(/&gt;/g, '>')
                                                   .replace(/&amp;/g, '&');
                    $('#sqlEditor').val(decodedSql);
                })
                .catch(error => {
                    console.error('获取SQL内容失败:', error);
                    $('#sqlEditor').val('获取SQL内容失败，请重试');
                });
        }

        // 修改关闭模态框函数
        function closeEditModal() {
            sqlEditModal.hide();
            currentEditingId = null;
            currentEditingVisitType = null;
        }

        // 修改保存SQL函数
        function saveSqlChanges() {
            const newSql = $('#sqlEditor').val();
            const table = $('#rulesTable').DataTable();
            const ruleId = currentEditingId;
            const visit_type = currentEditingVisitType;
            
            if (!ruleId || !visit_type) {
                showMessage('无法获取规则ID或就诊类型信息，请重新编辑', 'error');
                return;
            }
            
            // 添加调试日志
            console.log('准备保存SQL:', {
                rule_id: ruleId,
                visit_type: visit_type,
                sql_content_length: newSql.length,
                currentEditingId: currentEditingId,
                currentEditingVisitType: currentEditingVisitType
            });
            
            const currentPage = table.page();
            
            // 保存当前的筛选条件
            const currentFilters = {
                city: $('#city').val(),
                ruleSource: $('#ruleSource').val(),
                visit_type: $('#visitType').val(),
                ruleType: $('#ruleType').val(),
                ruleName: $('#ruleName').val(),
                ruleContent: $('#ruleContent').val()
            };
            
            //console.log('准备保存SQL:', {
            //    rule_id: currentEditingId,
            //    sql_content_length: newSql.length,
            //    visit_type: visit_type
            //});
            
            fetch('/api/update_sql', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    rule_id: currentEditingId,
                    sql_content: newSql,
                    visit_type: visit_type
                })
            })
            .then(response => {
                //console.log('收到响应:', response.status);
                return response.json();
            })
            .then(data => {
                //console.log('响应数据:', data);
                if (data.success) {
                    sqlEditModal.hide();
                    
                    // 仅重新加载当前表格数据，保持筛选条件
                    table.ajax.reload(function() {
                        // 恢复筛选条件
                        $('#city').val(currentFilters.city);
                        $('#ruleSource').val(currentFilters.ruleSource);
                        $('#visitType').val(currentFilters.visit_type);
                        $('#ruleType').val(currentFilters.ruleType);
                        $('#ruleName').val(currentFilters.ruleName);
                        $('#ruleContent').val(currentFilters.ruleContent);
                        // 恢复到之前的页码
                        table.page(currentPage).draw('page');
                    }, false);  // false 表示保持当前页面位置
                    
                    showMessage('保存成功', 'success');
                } else {
                    throw new Error(data.error || '保存失败');
                }
            })
            .catch(error => {
                console.error('保存SQL失败:', error);
                console.error('错误详情:', error.stack);
                showMessage('保存失败：' + error.message, 'error');
            });
        }

        // 测试函数 - 用于验证visit_type传递
        function testVisitTypePassing() {
            console.log('=== 测试visit_type传递 ===');
            console.log('currentEditingId:', currentEditingId);
            console.log('currentEditingVisitType:', currentEditingVisitType);
            
            if (currentEditingId && currentEditingVisitType) {
                console.log('✅ visit_type传递正常');
                showMessage('visit_type传递正常', 'success');
            } else {
                console.log('❌ visit_type传递异常');
                showMessage('visit_type传递异常', 'error');
            }
        }
        
        // 添加消息提示函数
        function showMessage(message, type = 'info') {
            const alertDiv = $(`
                <div class="alert alert-${type} alert-dismissible fade show position-fixed top-0 end-0 m-3" role="alert" style="z-index: 1050;">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `);
            
            $('body').append(alertDiv);
            
            // 3秒后自动关闭
            setTimeout(() => {
                alertDiv.alert('close');
            }, 3000);
        }

        // 加载筛选选项
        function loadFilterOptions() {
            fetch('/api/filter_options')
                .then(response => response.json())
                .then(data => {
                    // 填充城市下拉框
                    fillSelectOptions('city', data.cities);
                    // 填充规则来源下拉框
                    fillSelectOptions('ruleSource', data.rule_sources);
                    // 填充规则类型下拉框
                    fillSelectOptions('ruleType', data.rule_types);
                })
                .catch(error => {
                    console.error('加载筛选选项失败:', error);
                    showMessage('加载筛选选项失败，请刷新页面重试', 'error');
                });
        }

        // 加载医院选项
        function loadHospitalOptions() {
            fetch('/api/hospitals')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.hospitals) {
                        const hospitalSelect = document.getElementById('hospitalFilter');
                        hospitalSelect.innerHTML = '<option value="">全部医院</option>';

                        data.hospitals.forEach(hospital => {
                            const option = document.createElement('option');
                            option.value = hospital.医院ID;
                            option.textContent = hospital.医院名称;
                            hospitalSelect.appendChild(option);
                        });
                    }
                })
                .catch(error => {
                    console.error('加载医院列表失败:', error);
                    showMessage('加载医院列表失败', 'error');
                });
        }

        // 获取医院已采用规则的对照ID列表
        function getHospitalAdoptedRules(hospitalId) {
            if (!hospitalId) {
                hospitalAdoptedRuleIds = [];
                return Promise.resolve([]);
            }

            return fetch(`/api/hospital-rules/adopted/${hospitalId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.rules) {
                        // 直接从已采用规则中提取对照ID
                        hospitalAdoptedRuleIds = data.rules
                            .map(rule => rule.对照ID)
                            .filter(id => id != null)
                            .map(id => id.toString());
                        return hospitalAdoptedRuleIds;
                    }
                    return [];
                })
                .catch(error => {
                    console.error('获取医院已采用规则失败:', error);
                    showMessage('获取医院已采用规则失败', 'error');
                    return [];
                });
        }

        // 填充下拉框选项
        function fillSelectOptions(elementId, options) {
            const select = document.getElementById(elementId);
            const currentValue = select.value; // 保存当前选中值
            
            // 清空现有选项，保留默认选项
            select.innerHTML = `<option value="">选择${select.options[0].text.replace('选择', '')}</option>`;
            
            // 添加新选项
            options.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option;
                optionElement.textContent = option;
                select.appendChild(optionElement);
            });
            
            // 恢复之前的选中值
            if (currentValue) {
                select.value = currentValue;
            }
        }

        // 添加回车键搜索功能
        document.addEventListener('DOMContentLoaded', function() {
            // 为搜索输入框添加回车键事件监听
            document.getElementById('ruleName').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    searchRules();
                }
            });
            
            document.getElementById('ruleContent').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    searchRules();
                }
            });
            
            // 为下拉框添加回车键事件监听
            document.getElementById('city').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    searchRules();
                }
            });
            
            document.getElementById('ruleSource').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    searchRules();
                }
            });
            
            document.getElementById('behaviorType').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    searchRules();
                }
            });
            
            document.getElementById('ruleType').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    searchRules();
                }
            });

            // 为医院筛选器添加change事件监听
            document.getElementById('hospitalFilter').addEventListener('change', function(e) {
                searchRules();
            });
        });

        // 修改搜索函数，支持特殊字符和通配符
        function searchRules() {
            const hospitalId = $('#hospitalFilter').val();

            // 如果选择了医院，先获取该医院的已采用规则
            if (hospitalId) {
                showMessage('正在获取医院已采用规则...', 'info');
                getHospitalAdoptedRules(hospitalId).then(() => {
                    performSearch();
                });
            } else {
                hospitalAdoptedRuleIds = [];
                performSearch();
            }
        }

        // 执行实际的搜索逻辑
        function performSearch() {
            // 获取搜索条件
            const searchParams = {
                city: $('#city').val(),
                rule_source: $('#ruleSource').val(),
                behavior_type: $('#behaviorType').val(),
                rule_type: $('#ruleType').val(),
                type: $('#type').val(),
                rule_id: $('#ruleIdInput').val(),
                rule_name: $('#ruleName').val(),
                rule_content: $('#ruleContent').val(),
                visit_type: $('#visitType').val(),
                hospital_filter: $('#hospitalFilter').val()
            };
            
            console.log('搜索条件:', searchParams);
            
            // 获取DataTable实例
            const table = $('#rulesTable').DataTable();
            
            // 使用客户端过滤
            if (Object.values(searchParams).some(value => value)) {
                // 使用DataTables的搜索API进行客户端过滤
                $.fn.dataTable.ext.search.push(function(settings, data, dataIndex) {
                    // 检查每个搜索条件
                    if (searchParams.city && data[4] !== searchParams.city) return false;
                    if (searchParams.rule_source && data[5] !== searchParams.rule_source) return false;
                    if (searchParams.visit_type && data[6] !== searchParams.visit_type) return false;
                    if (searchParams.rule_type && data[7] !== searchParams.rule_type) return false;
                    if (searchParams.type && data[8] !== searchParams.type) return false;
                    
                    // 对文本字段使用包含搜索，支持特殊字符
                    if (searchParams.rule_name) {
                        const searchValue = processSearchValue(searchParams.rule_name);
                        const regex = new RegExp(searchValue, 'i');
                        if (!regex.test(data[3])) return false;
                    }
                    
                    if (searchParams.rule_content) {
                        const searchValue = processSearchValue(searchParams.rule_content);
                        const regex = new RegExp(searchValue, 'i');
                        if (!regex.test(data[9])) return false;
                    }

                    // 规则ID多值检索
                    if (searchParams.rule_id) {
                        // 支持分隔符 ; | , 空格
                        const idArr = searchParams.rule_id.split(/[;|,\s]+/).filter(Boolean);
                        if (idArr.length > 0) {
                            if (idArr.length === 1) {
                                if (data[2] !== idArr[0]) return false; // 规则ID列索引2
                            } else {
                                if (!idArr.includes(data[2])) return false;
                            }
                        }
                    }

                    // 医院筛选：只显示该医院已采用的规则
                    if (searchParams.hospital_filter && hospitalAdoptedRuleIds.length > 0) {
                        const compareId = data[1]; // 对照ID列索引1
                        if (!hospitalAdoptedRuleIds.includes(compareId.toString())) {
                            return false;
                        }
                    }

                    return true;
                });

                // 重绘表格应用过滤器
                table.draw();
                
                // 移除过滤器以避免影响后续操作
                $.fn.dataTable.ext.search.pop();
            } else {
                // 如果没有搜索条件，重置为原始数据
                table.ajax.url('/api/sql_history').load(function() {
                    // 重置全选框状态
                    document.getElementById('selectAll').checked = false;
                    // 更新选中计数
                    updateSelectedCount();
                });
            }
        }

        // 处理搜索值的函数，支持特殊字符和通配符
        function processSearchValue(value) {
            if (!value) return '';
            
            // 转义正则表达式中的特殊字符，但保留%
            let escaped = value.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
            
            // 将%替换为.*（正则表达式中的任意字符匹配）
            escaped = escaped.replace(/%/g, '.*');
            
            return escaped;
        }

        // 重置过滤器
        function resetFilters() {
            // 清空所有输入框和下拉框
            $('#city').val('');
            $('#ruleSource').val('');
            $('#behaviorType').val('');
            $('#ruleType').val('');
            $('#ruleName').val('');
            $('#ruleContent').val('');
            $('#hospitalFilter').val('');
            $('#visitType').val('');
            $('#type').val('');
            $('#ruleIdInput').val('');

            // 重置医院已采用规则列表
            hospitalAdoptedRuleIds = [];

            // 重置表格到初始状态
            const table = $('#rulesTable').DataTable();
            table.ajax.url('/api/sql_history').load(function() {
                // 重置全选框状态
                document.getElementById('selectAll').checked = false;
                // 更新选中计数
                updateSelectedCount();
            });
        }

        // 修复删除SQL函数，保留检索条件
        function deleteSql(ruleId, visitType) {
            if (!visitType) {
                showMessage('无法获取visit_type，删除失败', 'error');
                return;
            }
            if (!confirm('确定要删除此规则的SQL吗？此操作不可恢复。')) {
                return;
            }
            fetch(`/api/delete_sql/${ruleId}/${encodeURIComponent(visitType)}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('SQL记录已成功删除', 'success');
                    refreshTableWithCurrentFilters();
                } else {
                    showMessage('删除SQL记录失败：' + (data.error || '未知错误'), 'error');
                }
            })
            .catch(error => {
                console.error('删除SQL记录失败:', error);
                showMessage('删除SQL记录失败: ' + error.message, 'error');
            });
        }

        // 添加一个新函数，用于保留当前检索条件刷新表格
        function refreshTableWithCurrentFilters() {
            // 获取当前的检索条件
            const city = $('#city').val();
            const ruleSource = $('#ruleSource').val();
            const behaviorType = $('#behaviorType').val();
            const ruleType = $('#ruleType').val();
            const ruleName = $('#ruleName').val();
            const ruleContent = $('#ruleContent').val();
            const visitType = $('#visitType').val();
            const type = $('#type').val();
            const ruleIdInput = $('#ruleIdInput').val();
            const hospitalFilter = $('#hospitalFilter').val();

            // 如果有搜索条件，使用客户端过滤
            if (city || ruleSource || behaviorType || ruleType || ruleName || ruleContent || visitType || type || ruleIdInput || hospitalFilter) {
                // 先加载完整数据
                const table = $('#rulesTable').DataTable();
                table.ajax.url('/api/sql_history').load(function() {
                    // 然后应用搜索条件
                    searchRules();
                    // 重置全选框状态
                    document.getElementById('selectAll').checked = false;
                    // 更新选中计数
                    updateSelectedCount();
                });
            } else {
                // 如果没有搜索条件，直接加载原始数据
                const table = $('#rulesTable').DataTable();
                table.ajax.url('/api/sql_history').load(function() {
                    // 重置全选框状态
                    document.getElementById('selectAll').checked = false;
                    // 更新选中计数
                    updateSelectedCount();
                });
            }
        }

        // 添加全选/取消全选功能
        function toggleSelectAll() {
            const selectAllCheckbox = document.getElementById('selectAll');
            const isChecked = selectAllCheckbox.checked;
            
            // 获取所有规则复选框
            const checkboxes = document.querySelectorAll('.rule-checkbox');
            
            // 设置所有复选框状态与全选框一致
            checkboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
            });
            
            // 更新UI状态
            updateSelectedCount();
        }

        // 处理单个复选框变化
        function handleCheckboxChange() {
            // 更新选中数量
            updateSelectedCount();
            
            // 检查是否所有复选框都被选中
            const checkboxes = document.querySelectorAll('.rule-checkbox');
            const allChecked = Array.from(checkboxes).every(checkbox => checkbox.checked);
            
            // 更新全选框状态
            document.getElementById('selectAll').checked = allChecked;
        }

        // 更新选中规则数量显示
        function updateSelectedCount() {
            const selectedCount = document.querySelectorAll('.rule-checkbox:checked').length;
            const countElement = document.getElementById('selectedCount');
            const batchDeleteBtn = document.getElementById('batchDeleteBtn');

            if (countElement) {
                countElement.textContent = selectedCount;
            }

            // 控制批量删除按钮的启用状态
            if (batchDeleteBtn) {
                batchDeleteBtn.disabled = selectedCount === 0;
            }
        }

        // 获取选中的规则ID
        function getSelectedRuleIds() {
            const selectedCheckboxes = document.querySelectorAll('.rule-checkbox:checked');
            return Array.from(selectedCheckboxes).map(checkbox => checkbox.value);
        }

        // 批量删除SQL记录
        function batchDeleteSql() {
            const selectedRules = [];
            $('.rule-checkbox:checked').each(function() {
                const row = $(this).closest('tr');
                const cells = row.find('td');
                selectedRules.push({
                    compare_id: $(this).val(),
                    visit_type: cells.eq(6).text().trim() // 获取就诊类型
                });
            });

            if (selectedRules.length === 0) {
                showMessage('请至少选择一条规则', 'warning');
                return;
            }

            // 确认删除
            if (!confirm(`确定要删除选中的 ${selectedRules.length} 条SQL记录吗？此操作不可恢复！`)) {
                return;
            }

            // 显示加载状态
            const deleteBtn = document.getElementById('batchDeleteBtn');
            const originalText = deleteBtn.innerHTML;
            deleteBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 删除中...';
            deleteBtn.disabled = true;

            // 发送批量删除请求
            fetch('/api/batch_delete_sql', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    rules: selectedRules
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(`成功删除 ${data.deleted_count} 条SQL记录`, 'success');

                    // 使用现有的函数来保持筛选条件刷新表格
                    refreshTableWithCurrentFilters();

                    // 重置选中状态
                    document.getElementById('selectAll').checked = false;
                    updateSelectedCount();
                } else {
                    throw new Error(data.error || '删除失败');
                }
            })
            .catch(error => {
                console.error('批量删除失败:', error);
                showMessage('删除失败：' + error.message, 'error');
            })
            .finally(() => {
                // 恢复按钮状态
                deleteBtn.innerHTML = originalText;
                deleteBtn.disabled = false;
            });
        }

        // 修改导出SQL文件函数，正确处理二进制响应
        function exportSQLFile() {
            const selectedIds = getSelectedRuleIds();
            
            if (selectedIds.length === 0) {
                showMessage('请至少选择一条规则', 'error');
                return;
            }
            
            // 显示加载状态
            const exportBtn = document.getElementById('exportSQLBtn');
            const originalText = exportBtn.innerHTML;
            exportBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 导出中...';
            exportBtn.disabled = true;
            
            // 直接使用表单提交方式下载文件，避免JSON解析问题
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/api/export_system_sql';
            form.style.display = 'none';
            
            // 添加选中的规则ID
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'rule_ids';
            input.value = JSON.stringify(selectedIds);
            form.appendChild(input);
            
            // 添加到文档并提交
            document.body.appendChild(form);
            form.submit();
            
            // 恢复按钮状态
            setTimeout(() => {
                exportBtn.innerHTML = originalText;
                exportBtn.disabled = false;
                document.body.removeChild(form);
            }, 1000);
        }

        // 城市与省份映射关系
        const cityProvinceMapping = {
            '北京': { province_id: '11', province_name: '北京市', city_id: '110100' },
            '天津': { province_id: '12', province_name: '天津市', city_id: '120100' },
            '石家庄': { province_id: '13', province_name: '河北省', city_id: '130100' },
            '唐山': { province_id: '13', province_name: '河北省', city_id: '130200' },
            '秦皇岛': { province_id: '13', province_name: '河北省', city_id: '130300' },
            '邯郸': { province_id: '13', province_name: '河北省', city_id: '130400' },
            '邢台': { province_id: '13', province_name: '河北省', city_id: '130500' },
            '保定': { province_id: '13', province_name: '河北省', city_id: '130600' },
            '张家口': { province_id: '13', province_name: '河北省', city_id: '130700' },
            '承德': { province_id: '13', province_name: '河北省', city_id: '130800' },
            '沧州': { province_id: '13', province_name: '河北省', city_id: '130900' },
            '廊坊': { province_id: '13', province_name: '河北省', city_id: '131000' },
            '衡水': { province_id: '13', province_name: '河北省', city_id: '131100' },
            '太原': { province_id: '14', province_name: '山西省', city_id: '140100' },
            '大同': { province_id: '14', province_name: '山西省', city_id: '140200' },
            '阳泉': { province_id: '14', province_name: '山西省', city_id: '140300' },
            '长治': { province_id: '14', province_name: '山西省', city_id: '140400' },
            '晋城': { province_id: '14', province_name: '山西省', city_id: '140500' },
            '朔州': { province_id: '14', province_name: '山西省', city_id: '140600' },
            '晋中': { province_id: '14', province_name: '山西省', city_id: '140700' },
            '运城': { province_id: '14', province_name: '山西省', city_id: '140800' },
            '忻州': { province_id: '14', province_name: '山西省', city_id: '140900' },
            '临汾': { province_id: '14', province_name: '山西省', city_id: '141000' },
            '吕梁': { province_id: '14', province_name: '山西省', city_id: '141100' },
            '呼和浩特': { province_id: '15', province_name: '内蒙古自治区', city_id: '150100' },
            '包头': { province_id: '15', province_name: '内蒙古自治区', city_id: '150200' },
            '乌海': { province_id: '15', province_name: '内蒙古自治区', city_id: '150300' },
            '赤峰': { province_id: '15', province_name: '内蒙古自治区', city_id: '150400' },
            '通辽': { province_id: '15', province_name: '内蒙古自治区', city_id: '150500' },
            '鄂尔多斯': { province_id: '15', province_name: '内蒙古自治区', city_id: '150600' },
            '呼伦贝尔': { province_id: '15', province_name: '内蒙古自治区', city_id: '150700' },
            '巴彦淖尔': { province_id: '15', province_name: '内蒙古自治区', city_id: '150800' },
            '乌兰察布': { province_id: '15', province_name: '内蒙古自治区', city_id: '150900' },
            '兴安盟': { province_id: '15', province_name: '内蒙古自治区', city_id: '152200' },
            '锡林郭勒盟': { province_id: '15', province_name: '内蒙古自治区', city_id: '152500' },
            '阿拉善盟': { province_id: '15', province_name: '内蒙古自治区', city_id: '152900' },
            '沈阳': { province_id: '21', province_name: '辽宁省', city_id: '210100' },
            '大连': { province_id: '21', province_name: '辽宁省', city_id: '210200' },
            '鞍山': { province_id: '21', province_name: '辽宁省', city_id: '210300' },
            '抚顺': { province_id: '21', province_name: '辽宁省', city_id: '210400' },
            '本溪': { province_id: '21', province_name: '辽宁省', city_id: '210500' },
            '丹东': { province_id: '21', province_name: '辽宁省', city_id: '210600' },
            '锦州': { province_id: '21', province_name: '辽宁省', city_id: '210700' },
            '营口': { province_id: '21', province_name: '辽宁省', city_id: '210800' },
            '阜新': { province_id: '21', province_name: '辽宁省', city_id: '210900' },
            '辽阳': { province_id: '21', province_name: '辽宁省', city_id: '211000' },
            '盘锦': { province_id: '21', province_name: '辽宁省', city_id: '211100' },
            '铁岭': { province_id: '21', province_name: '辽宁省', city_id: '211200' },
            '朝阳': { province_id: '21', province_name: '辽宁省', city_id: '211300' },
            '葫芦岛': { province_id: '21', province_name: '辽宁省', city_id: '211400' },
            '长春': { province_id: '22', province_name: '吉林省', city_id: '220100' },
            '吉林': { province_id: '22', province_name: '吉林省', city_id: '220200' },
            '四平': { province_id: '22', province_name: '吉林省', city_id: '220300' },
            '辽源': { province_id: '22', province_name: '吉林省', city_id: '220400' },
            '通化': { province_id: '22', province_name: '吉林省', city_id: '220500' },
            '白山': { province_id: '22', province_name: '吉林省', city_id: '220600' },
            '松原': { province_id: '22', province_name: '吉林省', city_id: '220700' },
            '白城': { province_id: '22', province_name: '吉林省', city_id: '220800' },
            '延边': { province_id: '22', province_name: '吉林省', city_id: '222400' },
            '哈尔滨': { province_id: '23', province_name: '黑龙江省', city_id: '230100' },
            '齐齐哈尔': { province_id: '23', province_name: '黑龙江省', city_id: '230200' },
            '鸡西': { province_id: '23', province_name: '黑龙江省', city_id: '230300' },
            '鹤岗': { province_id: '23', province_name: '黑龙江省', city_id: '230400' },
            '双鸭山': { province_id: '23', province_name: '黑龙江省', city_id: '230500' },
            '大庆': { province_id: '23', province_name: '黑龙江省', city_id: '230600' },
            '伊春': { province_id: '23', province_name: '黑龙江省', city_id: '230700' },
            '佳木斯': { province_id: '23', province_name: '黑龙江省', city_id: '230800' },
            '七台河': { province_id: '23', province_name: '黑龙江省', city_id: '230900' },
            '牡丹江': { province_id: '23', province_name: '黑龙江省', city_id: '231000' },
            '黑河': { province_id: '23', province_name: '黑龙江省', city_id: '231100' },
            '绥化': { province_id: '23', province_name: '黑龙江省', city_id: '231200' },
            '大兴安岭': { province_id: '23', province_name: '黑龙江省', city_id: '232700' },
            '上海': { province_id: '31', province_name: '上海市', city_id: '310100' },
            '南京': { province_id: '32', province_name: '江苏省', city_id: '320100' },
            '无锡': { province_id: '32', province_name: '江苏省', city_id: '320200' },
            '徐州': { province_id: '32', province_name: '江苏省', city_id: '320300' },
            '常州': { province_id: '32', province_name: '江苏省', city_id: '320400' },
            '苏州': { province_id: '32', province_name: '江苏省', city_id: '320500' },
            '南通': { province_id: '32', province_name: '江苏省', city_id: '320600' },
            '连云港': { province_id: '32', province_name: '江苏省', city_id: '320700' },
            '淮安': { province_id: '32', province_name: '江苏省', city_id: '320800' },
            '盐城': { province_id: '32', province_name: '江苏省', city_id: '320900' },
            '扬州': { province_id: '32', province_name: '江苏省', city_id: '321000' },
            '镇江': { province_id: '32', province_name: '江苏省', city_id: '321100' },
            '泰州': { province_id: '32', province_name: '江苏省', city_id: '321200' },
            '宿迁': { province_id: '32', province_name: '江苏省', city_id: '321300' },
            '杭州': { province_id: '33', province_name: '浙江省', city_id: '330100' },
            '宁波': { province_id: '33', province_name: '浙江省', city_id: '330200' },
            '温州': { province_id: '33', province_name: '浙江省', city_id: '330300' },
            '嘉兴': { province_id: '33', province_name: '浙江省', city_id: '330400' },
            '湖州': { province_id: '33', province_name: '浙江省', city_id: '330500' },
            '绍兴': { province_id: '33', province_name: '浙江省', city_id: '330600' },
            '金华': { province_id: '33', province_name: '浙江省', city_id: '330700' },
            '衢州': { province_id: '33', province_name: '浙江省', city_id: '330800' },
            '舟山': { province_id: '33', province_name: '浙江省', city_id: '330900' },
            '台州': { province_id: '33', province_name: '浙江省', city_id: '331000' },
            '丽水': { province_id: '33', province_name: '浙江省', city_id: '331100' },
            '合肥': { province_id: '34', province_name: '安徽省', city_id: '340100' },
            '芜湖': { province_id: '34', province_name: '安徽省', city_id: '340200' },
            '蚌埠': { province_id: '34', province_name: '安徽省', city_id: '340300' },
            '淮南': { province_id: '34', province_name: '安徽省', city_id: '340400' },
            '马鞍山': { province_id: '34', province_name: '安徽省', city_id: '340500' },
            '淮北': { province_id: '34', province_name: '安徽省', city_id: '340600' },
            '铜陵': { province_id: '34', province_name: '安徽省', city_id: '340700' },
            '安庆': { province_id: '34', province_name: '安徽省', city_id: '340800' },
            '黄山': { province_id: '34', province_name: '安徽省', city_id: '341000' },
            '滁州': { province_id: '34', province_name: '安徽省', city_id: '341100' },
            '阜阳': { province_id: '34', province_name: '安徽省', city_id: '341200' },
            '宿州': { province_id: '34', province_name: '安徽省', city_id: '341300' },
            '六安': { province_id: '34', province_name: '安徽省', city_id: '341500' },
            '亳州': { province_id: '34', province_name: '安徽省', city_id: '341600' },
            '池州': { province_id: '34', province_name: '安徽省', city_id: '341700' },
            '宣城': { province_id: '34', province_name: '安徽省', city_id: '341800' },
            '福州': { province_id: '35', province_name: '福建省', city_id: '350100' },
            '厦门': { province_id: '35', province_name: '福建省', city_id: '350200' },
            '莆田': { province_id: '35', province_name: '福建省', city_id: '350300' },
            '三明': { province_id: '35', province_name: '福建省', city_id: '350400' },
            '泉州': { province_id: '35', province_name: '福建省', city_id: '350500' },
            '漳州': { province_id: '35', province_name: '福建省', city_id: '350600' },
            '南平': { province_id: '35', province_name: '福建省', city_id: '350700' },
            '龙岩': { province_id: '35', province_name: '福建省', city_id: '350800' },
            '宁德': { province_id: '35', province_name: '福建省', city_id: '350900' },
            '南昌': { province_id: '36', province_name: '江西省', city_id: '360100' },
            '景德镇': { province_id: '36', province_name: '江西省', city_id: '360200' },
            '萍乡': { province_id: '36', province_name: '江西省', city_id: '360300' },
            '九江': { province_id: '36', province_name: '江西省', city_id: '360400' },
            '新余': { province_id: '36', province_name: '江西省', city_id: '360500' },
            '鹰潭': { province_id: '36', province_name: '江西省', city_id: '360600' },
            '赣州': { province_id: '36', province_name: '江西省', city_id: '360700' },
            '吉安': { province_id: '36', province_name: '江西省', city_id: '360800' },
            '宜春': { province_id: '36', province_name: '江西省', city_id: '360900' },
            '抚州': { province_id: '36', province_name: '江西省', city_id: '361000' },
            '上饶': { province_id: '36', province_name: '江西省', city_id: '361100' },
            '济南': { province_id: '37', province_name: '山东省', city_id: '370100' },
            '青岛': { province_id: '37', province_name: '山东省', city_id: '370200' },
            '淄博': { province_id: '37', province_name: '山东省', city_id: '370300' },
            '枣庄': { province_id: '37', province_name: '山东省', city_id: '370400' },
            '东营': { province_id: '37', province_name: '山东省', city_id: '370500' },
            '烟台': { province_id: '37', province_name: '山东省', city_id: '370600' },
            '潍坊': { province_id: '37', province_name: '山东省', city_id: '370700' },
            '济宁': { province_id: '37', province_name: '山东省', city_id: '370800' },
            '泰安': { province_id: '37', province_name: '山东省', city_id: '370900' },
            '威海': { province_id: '37', province_name: '山东省', city_id: '371000' },
            '日照': { province_id: '37', province_name: '山东省', city_id: '371100' },
            '莱芜': { province_id: '37', province_name: '山东省', city_id: '371200' },
            '临沂': { province_id: '37', province_name: '山东省', city_id: '371300' },
            '德州': { province_id: '37', province_name: '山东省', city_id: '371400' },
            '聊城': { province_id: '37', province_name: '山东省', city_id: '371500' },
            '滨州': { province_id: '37', province_name: '山东省', city_id: '371600' },
            '菏泽': { province_id: '37', province_name: '山东省', city_id: '371700' },
            '郑州': { province_id: '41', province_name: '河南省', city_id: '410100' },
            '开封': { province_id: '41', province_name: '河南省', city_id: '410200' },
            '洛阳': { province_id: '41', province_name: '河南省', city_id: '410300' },
            '平顶山': { province_id: '41', province_name: '河南省', city_id: '410400' },
            '安阳': { province_id: '41', province_name: '河南省', city_id: '410500' },
            '鹤壁': { province_id: '41', province_name: '河南省', city_id: '410600' },
            '新乡': { province_id: '41', province_name: '河南省', city_id: '410700' },
            '焦作': { province_id: '41', province_name: '河南省', city_id: '410800' },
            '濮阳': { province_id: '41', province_name: '河南省', city_id: '410900' },
            '许昌': { province_id: '41', province_name: '河南省', city_id: '411000' },
            '漯河': { province_id: '41', province_name: '河南省', city_id: '411100' },
            '三门峡': { province_id: '41', province_name: '河南省', city_id: '411200' },
            '南阳': { province_id: '41', province_name: '河南省', city_id: '411300' },
            '商丘': { province_id: '41', province_name: '河南省', city_id: '411400' },
            '信阳': { province_id: '41', province_name: '河南省', city_id: '411500' },
            '周口': { province_id: '41', province_name: '河南省', city_id: '411600' },
            '驻马店': { province_id: '41', province_name: '河南省', city_id: '411700' },
            '济源': { province_id: '41', province_name: '河南省', city_id: '419001' },
            '武汉': { province_id: '42', province_name: '湖北省', city_id: '420100' },
            '黄石': { province_id: '42', province_name: '湖北省', city_id: '420200' },
            '十堰': { province_id: '42', province_name: '湖北省', city_id: '420300' },
            '宜昌': { province_id: '42', province_name: '湖北省', city_id: '420500' },
            '襄阳': { province_id: '42', province_name: '湖北省', city_id: '420600' },
            '鄂州': { province_id: '42', province_name: '湖北省', city_id: '420700' },
            '荆门': { province_id: '42', province_name: '湖北省', city_id: '420800' },
            '孝感': { province_id: '42', province_name: '湖北省', city_id: '420900' },
            '荆州': { province_id: '42', province_name: '湖北省', city_id: '421000' },
            '黄冈': { province_id: '42', province_name: '湖北省', city_id: '421100' },
            '咸宁': { province_id: '42', province_name: '湖北省', city_id: '421200' },
            '随州': { province_id: '42', province_name: '湖北省', city_id: '421300' },
            '恩施': { province_id: '42', province_name: '湖北省', city_id: '422800' },
            '仙桃': { province_id: '42', province_name: '湖北省', city_id: '429004' },
            '潜江': { province_id: '42', province_name: '湖北省', city_id: '429005' },
            '天门': { province_id: '42', province_name: '湖北省', city_id: '429006' },
            '神农架': { province_id: '42', province_name: '湖北省', city_id: '429021' },
            '长沙': { province_id: '43', province_name: '湖南省', city_id: '430100' },
            '株洲': { province_id: '43', province_name: '湖南省', city_id: '430200' },
            '湘潭': { province_id: '43', province_name: '湖南省', city_id: '430300' },
            '衡阳': { province_id: '43', province_name: '湖南省', city_id: '430400' },
            '邵阳': { province_id: '43', province_name: '湖南省', city_id: '430500' },
            '岳阳': { province_id: '43', province_name: '湖南省', city_id: '430600' },
            '常德': { province_id: '43', province_name: '湖南省', city_id: '430700' },
            '张家界': { province_id: '43', province_name: '湖南省', city_id: '430800' },
            '益阳': { province_id: '43', province_name: '湖南省', city_id: '430900' },
            '郴州': { province_id: '43', province_name: '湖南省', city_id: '431000' },
            '永州': { province_id: '43', province_name: '湖南省', city_id: '431100' },
            '怀化': { province_id: '43', province_name: '湖南省', city_id: '431200' },
            '娄底': { province_id: '43', province_name: '湖南省', city_id: '431300' },
            '湘西': { province_id: '43', province_name: '湖南省', city_id: '433100' },
            '广州': { province_id: '44', province_name: '广东省', city_id: '440100' },
            '韶关': { province_id: '44', province_name: '广东省', city_id: '440200' },
            '深圳': { province_id: '44', province_name: '广东省', city_id: '440300' },
            '珠海': { province_id: '44', province_name: '广东省', city_id: '440400' },
            '汕头': { province_id: '44', province_name: '广东省', city_id: '440500' },
            '佛山': { province_id: '44', province_name: '广东省', city_id: '440600' },
            '江门': { province_id: '44', province_name: '广东省', city_id: '440700' },
            '湛江': { province_id: '44', province_name: '广东省', city_id: '440800' },
            '茂名': { province_id: '44', province_name: '广东省', city_id: '440900' },
            '肇庆': { province_id: '44', province_name: '广东省', city_id: '441200' },
            '惠州': { province_id: '44', province_name: '广东省', city_id: '441300' },
            '梅州': { province_id: '44', province_name: '广东省', city_id: '441400' },
            '汕尾': { province_id: '44', province_name: '广东省', city_id: '441500' },
            '河源': { province_id: '44', province_name: '广东省', city_id: '441600' },
            '阳江': { province_id: '44', province_name: '广东省', city_id: '441700' },
            '清远': { province_id: '44', province_name: '广东省', city_id: '441800' },
            '东莞': { province_id: '44', province_name: '广东省', city_id: '441900' },
            '中山': { province_id: '44', province_name: '广东省', city_id: '442000' },
            '潮州': { province_id: '44', province_name: '广东省', city_id: '445100' },
            '揭阳': { province_id: '44', province_name: '广东省', city_id: '445200' },
            '云浮': { province_id: '44', province_name: '广东省', city_id: '445300' },
            '南宁': { province_id: '45', province_name: '广西壮族自治区', city_id: '450100' },
            '柳州': { province_id: '45', province_name: '广西壮族自治区', city_id: '450200' },
            '桂林': { province_id: '45', province_name: '广西壮族自治区', city_id: '450300' },
            '梧州': { province_id: '45', province_name: '广西壮族自治区', city_id: '450400' },
            '北海': { province_id: '45', province_name: '广西壮族自治区', city_id: '450500' },
            '防城港': { province_id: '45', province_name: '广西壮族自治区', city_id: '450600' },
            '钦州': { province_id: '45', province_name: '广西壮族自治区', city_id: '450700' },
            '贵港': { province_id: '45', province_name: '广西壮族自治区', city_id: '450800' },
            '玉林': { province_id: '45', province_name: '广西壮族自治区', city_id: '450900' },
            '百色': { province_id: '45', province_name: '广西壮族自治区', city_id: '451000' },
            '贺州': { province_id: '45', province_name: '广西壮族自治区', city_id: '451100' },
            '河池': { province_id: '45', province_name: '广西壮族自治区', city_id: '451200' },
            '来宾': { province_id: '45', province_name: '广西壮族自治区', city_id: '451300' },
            '崇左': { province_id: '45', province_name: '广西壮族自治区', city_id: '451400' },
            '海口': { province_id: '46', province_name: '海南省', city_id: '460100' },
            '三亚': { province_id: '46', province_name: '海南省', city_id: '460200' },
            '三沙': { province_id: '46', province_name: '海南省', city_id: '460300' },
            '儋州': { province_id: '46', province_name: '海南省', city_id: '460400' },
            '重庆': { province_id: '50', province_name: '重庆市', city_id: '500100' },
            '成都': { province_id: '51', province_name: '四川省', city_id: '510100' },
            '自贡': { province_id: '51', province_name: '四川省', city_id: '510300' },
            '攀枝花': { province_id: '51', province_name: '四川省', city_id: '510400' },
            '泸州': { province_id: '51', province_name: '四川省', city_id: '510500' },
            '德阳': { province_id: '51', province_name: '四川省', city_id: '510600' },
            '绵阳': { province_id: '51', province_name: '四川省', city_id: '510700' },
            '广元': { province_id: '51', province_name: '四川省', city_id: '510800' },
            '遂宁': { province_id: '51', province_name: '四川省', city_id: '510900' },
            '内江': { province_id: '51', province_name: '四川省', city_id: '511000' },
            '乐山': { province_id: '51', province_name: '四川省', city_id: '511100' },
            '南充': { province_id: '51', province_name: '四川省', city_id: '511300' },
            '眉山': { province_id: '51', province_name: '四川省', city_id: '511400' },
            '宜宾': { province_id: '51', province_name: '四川省', city_id: '511500' },
            '广安': { province_id: '51', province_name: '四川省', city_id: '511600' },
            '达州': { province_id: '51', province_name: '四川省', city_id: '511700' },
            '雅安': { province_id: '51', province_name: '四川省', city_id: '511800' },
            '巴中': { province_id: '51', province_name: '四川省', city_id: '511900' },
            '资阳': { province_id: '51', province_name: '四川省', city_id: '512000' },
            '阿坝': { province_id: '51', province_name: '四川省', city_id: '513200' },
            '甘孜': { province_id: '51', province_name: '四川省', city_id: '513300' },
            '凉山': { province_id: '51', province_name: '四川省', city_id: '513400' },
            '贵阳': { province_id: '52', province_name: '贵州省', city_id: '520100' },
            '遵义': { province_id: '52', province_name: '贵州省', city_id: '520300' },
            '安顺': { province_id: '52', province_name: '贵州省', city_id: '520400' },
            '毕节': { province_id: '52', province_name: '贵州省', city_id: '520500' },
            '铜仁': { province_id: '52', province_name: '贵州省', city_id: '520600' },
            '黔西南': { province_id: '52', province_name: '贵州省', city_id: '522300' },
            '黔东南': { province_id: '52', province_name: '贵州省', city_id: '522600' },
            '黔南': { province_id: '52', province_name: '贵州省', city_id: '522700' },
            '昆明': { province_id: '53', province_name: '云南省', city_id: '530100' },
            '曲靖': { province_id: '53', province_name: '云南省', city_id: '530300' },
            '玉溪': { province_id: '53', province_name: '云南省', city_id: '530400' },
            '保山': { province_id: '53', province_name: '云南省', city_id: '530500' },
            '昭通': { province_id: '53', province_name: '云南省', city_id: '530600' },
            '丽江': { province_id: '53', province_name: '云南省', city_id: '530700' },
            '普洱': { province_id: '53', province_name: '云南省', city_id: '530800' },
            '临沧': { province_id: '53', province_name: '云南省', city_id: '530900' },
            '楚雄': { province_id: '53', province_name: '云南省', city_id: '532300' },
            '红河': { province_id: '53', province_name: '云南省', city_id: '532500' },
            '文山': { province_id: '53', province_name: '云南省', city_id: '532600' },
            '西双版纳': { province_id: '53', province_name: '云南省', city_id: '532800' },
            '大理': { province_id: '53', province_name: '云南省', city_id: '532900' },
            '德宏': { province_id: '53', province_name: '云南省', city_id: '533100' },
            '怒江': { province_id: '53', province_name: '云南省', city_id: '533300' },
            '迪庆': { province_id: '53', province_name: '云南省', city_id: '533400' },
            '拉萨': { province_id: '54', province_name: '西藏自治区', city_id: '540100' },
            '日喀则': { province_id: '54', province_name: '西藏自治区', city_id: '540200' },
            '昌都': { province_id: '54', province_name: '西藏自治区', city_id: '540300' },
            '林芝': { province_id: '54', province_name: '西藏自治区', city_id: '540400' },
            '山南': { province_id: '54', province_name: '西藏自治区', city_id: '540500' },
            '那曲': { province_id: '54', province_name: '西藏自治区', city_id: '540600' },
            '阿里': { province_id: '54', province_name: '西藏自治区', city_id: '542500' },
            '西安': { province_id: '61', province_name: '陕西省', city_id: '610100' },
            '铜川': { province_id: '61', province_name: '陕西省', city_id: '610200' },
            '宝鸡': { province_id: '61', province_name: '陕西省', city_id: '610300' },
            '咸阳': { province_id: '61', province_name: '陕西省', city_id: '610400' },
            '渭南': { province_id: '61', province_name: '陕西省', city_id: '610500' },
            '延安': { province_id: '61', province_name: '陕西省', city_id: '610600' },
            '汉中': { province_id: '61', province_name: '陕西省', city_id: '610700' },
            '榆林': { province_id: '61', province_name: '陕西省', city_id: '610800' },
            '安康': { province_id: '61', province_name: '陕西省', city_id: '610900' },
            '商洛': { province_id: '61', province_name: '陕西省', city_id: '611000' },
            '兰州': { province_id: '62', province_name: '甘肃省', city_id: '620100' },
            '嘉峪关': { province_id: '62', province_name: '甘肃省', city_id: '620200' },
            '金昌': { province_id: '62', province_name: '甘肃省', city_id: '620300' },
            '白银': { province_id: '62', province_name: '甘肃省', city_id: '620400' },
            '天水': { province_id: '62', province_name: '甘肃省', city_id: '620500' },
            '武威': { province_id: '62', province_name: '甘肃省', city_id: '620600' },
            '张掖': { province_id: '62', province_name: '甘肃省', city_id: '620700' },
            '平凉': { province_id: '62', province_name: '甘肃省', city_id: '620800' },
            '酒泉': { province_id: '62', province_name: '甘肃省', city_id: '620900' },
            '庆阳': { province_id: '62', province_name: '甘肃省', city_id: '621000' },
            '定西': { province_id: '62', province_name: '甘肃省', city_id: '621100' },
            '陇南': { province_id: '62', province_name: '甘肃省', city_id: '621200' },
            '临夏': { province_id: '62', province_name: '甘肃省', city_id: '622900' },
            '甘南': { province_id: '62', province_name: '甘肃省', city_id: '623000' },
            '西宁': { province_id: '63', province_name: '青海省', city_id: '630100' },
            '海东': { province_id: '63', province_name: '青海省', city_id: '630200' },
            '海北': { province_id: '63', province_name: '青海省', city_id: '632200' },
            '黄南': { province_id: '63', province_name: '青海省', city_id: '632300' },
            '海南': { province_id: '63', province_name: '青海省', city_id: '632500' },
            '果洛': { province_id: '63', province_name: '青海省', city_id: '632600' },
            '玉树': { province_id: '63', province_name: '青海省', city_id: '632700' },
            '海西': { province_id: '63', province_name: '青海省', city_id: '632800' },
            '银川': { province_id: '64', province_name: '宁夏回族自治区', city_id: '640100' },
            '石嘴山': { province_id: '64', province_name: '宁夏回族自治区', city_id: '640200' },
            '吴忠': { province_id: '64', province_name: '宁夏回族自治区', city_id: '640300' },
            '固原': { province_id: '64', province_name: '宁夏回族自治区', city_id: '640400' },
            '中卫': { province_id: '64', province_name: '宁夏回族自治区', city_id: '640500' },
            '乌鲁木齐': { province_id: '65', province_name: '新疆维吾尔自治区', city_id: '650100' },
            '克拉玛依': { province_id: '65', province_name: '新疆维吾尔自治区', city_id: '650200' },
            '吐鲁番': { province_id: '65', province_name: '新疆维吾尔自治区', city_id: '650400' },
            '哈密': { province_id: '65', province_name: '新疆维吾尔自治区', city_id: '650500' },
            '昌吉': { province_id: '65', province_name: '新疆维吾尔自治区', city_id: '652300' },
            '博尔塔拉': { province_id: '65', province_name: '新疆维吾尔自治区', city_id: '652700' },
            '巴音郭楞': { province_id: '65', province_name: '新疆维吾尔自治区', city_id: '652800' },
            '阿克苏': { province_id: '65', province_name: '新疆维吾尔自治区', city_id: '652900' },
            '克孜勒苏': { province_id: '65', province_name: '新疆维吾尔自治区', city_id: '653000' },
            '喀什': { province_id: '65', province_name: '新疆维吾尔自治区', city_id: '653100' },
            '和田': { province_id: '65', province_name: '新疆维吾尔自治区', city_id: '653200' },
            '伊犁': { province_id: '65', province_name: '新疆维吾尔自治区', city_id: '654000' },
            '塔城': { province_id: '65', province_name: '新疆维吾尔自治区', city_id: '654200' },
            '阿勒泰': { province_id: '65', province_name: '新疆维吾尔自治区', city_id: '654300' },
            '石河子': { province_id: '65', province_name: '新疆维吾尔自治区', city_id: '659001' },
            '阿拉尔': { province_id: '65', province_name: '新疆维吾尔自治区', city_id: '659002' },
            '图木舒克': { province_id: '65', province_name: '新疆维吾尔自治区', city_id: '659003' },
            '五家渠': { province_id: '65', province_name: '新疆维吾尔自治区', city_id: '659004' },
            '北屯': { province_id: '65', province_name: '新疆维吾尔自治区', city_id: '659005' },
            '铁门关': { province_id: '65', province_name: '新疆维吾尔自治区', city_id: '659006' },
            '双河': { province_id: '65', province_name: '新疆维吾尔自治区', city_id: '659007' },
            '可克达拉': { province_id: '65', province_name: '新疆维吾尔自治区', city_id: '659008' },
            '昆玉': { province_id: '65', province_name: '新疆维吾尔自治区', city_id: '659009' },
            '胡杨河': { province_id: '65', province_name: '新疆维吾尔自治区', city_id: '659010' }
        };

        // 根据城市名称获取配置信息
        function getCityConfig(cityName) {
            return cityProvinceMapping[cityName] || {
                province_id: '32',
                province_name: '江苏省',
                city_id: '320100'
            };
        }

        // 生成医院编码
        function generateHospitalCode(cityId) {
            return `H${cityId}0000001`;
        }

        // 生成医院名称
        function generateHospitalName(cityName) {
            return `${cityName}市第一人民医院`;
        }

        function importToPG() {
            const selectedRuleId = document.querySelector('#rulesTable tbody tr td input[type="checkbox"]:checked')?.value;
            
            if (!selectedRuleId) {
                showMessage('请选择一条规则', 'warning');
                return;
            }

            // 获取选中规则的信息
            const selectedRow = document.querySelector('#rulesTable tbody tr td input[type="checkbox"]:checked').closest('tr');
            const cells = selectedRow.querySelectorAll('td');
            const ruleName = cells[3].textContent.trim(); // 第4列是规则名称
            const cityName = cells[4].textContent.trim(); // 第5列是城市
            const visitType = cells[6].textContent.trim(); // 第7列是就诊类型

            // 更新当前规则信息显示
            document.getElementById('currentRuleInfo').textContent = `${ruleName} (${cityName} - ${visitType})`;

            // 初始化城市选择器
            initializeCitySelector(cityName);

            // 根据城市信息自动填充配置
            const cityConfig = getCityConfig(cityName);
            const hospitalCode = generateHospitalCode(cityConfig.city_id);
            const hospitalName = generateHospitalName(cityName);

            // 填充表单
            document.querySelector('input[name="province_id"]').value = cityConfig.province_id;
            document.querySelector('input[name="province_name"]').value = cityConfig.province_name;
            document.querySelector('input[name="city_id"]').value = cityConfig.city_id;
            document.querySelector('input[name="city_name"]').value = cityName;
            document.querySelector('input[name="hospital_code"]').value = hospitalCode;
            document.querySelector('input[name="hospital_name"]').value = hospitalName;

            // 更新配置预览
            updateConfigPreview();

            // 显示配置模态框
            const modal = new bootstrap.Modal(document.getElementById('pgImportModal'));
            modal.show();

            // 监听模态框显示事件，确保城市选择器正确初始化
            document.getElementById('pgImportModal').addEventListener('shown.bs.modal', function() {
                // 重新初始化城市选择器，确保DOM已完全加载
                setTimeout(() => {
                    initializeCitySelector(cityName);
                }, 100);
            });

            // 为表单字段添加变化监听器，自动更新配置预览
            const formFields = document.querySelectorAll('#pgImportForm input, #pgImportForm select');
            formFields.forEach(field => {
                field.addEventListener('input', updateConfigPreview);
                field.addEventListener('change', updateConfigPreview);
            });
        }

        // 初始化城市选择器
        function initializeCitySelector(selectedCity = '') {
            const citySelector = document.getElementById('citySelector');
            citySelector.innerHTML = '<option value="">请选择城市</option>';
            
            // 按省份分组城市
            const provinces = {};
            Object.entries(cityProvinceMapping).forEach(([cityName, config]) => {
                const provinceName = config.province_name;
                if (!provinces[provinceName]) {
                    provinces[provinceName] = [];
                }
                provinces[provinceName].push({ cityName, config });
            });

            // 添加城市选项
            Object.entries(provinces).forEach(([provinceName, cities]) => {
                const optgroup = document.createElement('optgroup');
                optgroup.label = provinceName;
                
                cities.forEach(({ cityName, config }) => {
                    const option = document.createElement('option');
                    option.value = cityName;
                    option.textContent = cityName;
                    if (cityName === selectedCity) {
                        option.selected = true;
                    }
                    optgroup.appendChild(option);
                });
                
                citySelector.appendChild(optgroup);
            });
        }

        // 处理城市选择变化
        function onCityChange() {
            const citySelector = document.getElementById('citySelector');
            const selectedCity = citySelector.value;
            
            if (!selectedCity) {
                return;
            }

            // 根据城市信息自动填充配置
            const cityConfig = getCityConfig(selectedCity);
            const hospitalCode = generateHospitalCode(cityConfig.city_id);
            const hospitalName = generateHospitalName(selectedCity);

            // 填充表单
            document.querySelector('input[name="province_id"]').value = cityConfig.province_id;
            document.querySelector('input[name="province_name"]').value = cityConfig.province_name;
            document.querySelector('input[name="city_id"]').value = cityConfig.city_id;
            document.querySelector('input[name="city_name"]').value = selectedCity;
            document.querySelector('input[name="hospital_code"]').value = hospitalCode;
            document.querySelector('input[name="hospital_name"]').value = hospitalName;

            // 更新配置预览
            updateConfigPreview();
        }

        // 更新配置预览
        function updateConfigPreview() {
            const provinceId = document.querySelector('input[name="province_id"]').value;
            const provinceName = document.querySelector('input[name="province_name"]').value;
            const cityId = document.querySelector('input[name="city_id"]').value;
            const cityName = document.querySelector('input[name="city_name"]').value;
            const hospitalCode = document.querySelector('input[name="hospital_code"]').value;
            const hospitalName = document.querySelector('input[name="hospital_name"]').value;
            const orgCode = document.querySelector('input[name="org_code"]').value;
            const ybOrYy = document.querySelector('select[name="yb_or_yy"]').value;

            document.getElementById('previewProvince').textContent = `${provinceName} (${provinceId})`;
            document.getElementById('previewCity').textContent = `${cityName} (${cityId})`;
            document.getElementById('previewHospital').textContent = `${hospitalName} (${hospitalCode})`;
            document.getElementById('previewOrg').textContent = `${orgCode} (${ybOrYy === '1' ? '医保' : '医院'})`;
        }

        function submitImport() {
            // 获取所有选中的规则及其就诊类型
            const selectedRules = [];
            document.querySelectorAll('#rulesTable tbody tr td input[type="checkbox"]:checked').forEach(checkbox => {
                const row = checkbox.closest('tr');
                const cells = row.querySelectorAll('td');
                const visitType = cells[6].textContent.trim(); // 获取第7列的就诊类型值

                selectedRules.push({
                    id: checkbox.value,
                    visit_type: visitType
                });
            });

            if (selectedRules.length === 0) {
                showMessage('请至少选择一条规则', 'warning');
                return;
            }

            const formData = new FormData(document.getElementById('pgImportForm'));
            const baseParams = new URLSearchParams();

            // 将表单数据转换为URL参数（不包含visit_type，因为每个规则有自己的visit_type）
            for (const [key, value] of formData.entries()) {
                baseParams.append(key, value);
            }

            console.log("选择的规则:", selectedRules);
            console.log("基础表单数据:", baseParams.toString());

            // 获取每个选中规则的SQL内容并导入
            Promise.all(selectedRules.map(rule => {
                const params = new URLSearchParams(baseParams);
                params.append('visit_type', rule.visit_type); // 为每个规则添加其对应的就诊类型

                return fetch(`/api/import_sql_content/${rule.id}?${params.toString()}`)
                    .then(response => response.json());
            }))
            .then(responses => {
                const validResponses = responses.filter(data => !data.error);
                if (validResponses.length === 0) {
                    showMessage('获取SQL失败或没有有效的SQL内容', 'error');
                    return;
                }

                // 显示加载状态
                showMessage('正在导入...', 'info');

                // 发送完整数据到后端
                return fetch('/api/import_to_pg', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(validResponses)
                });
            })
            .then(response => response?.json())
            .then(data => {
                if (!data) return;
                
                // 关闭模态框
                bootstrap.Modal.getInstance(document.getElementById('pgImportModal')).hide();
                
                if (data.success) {
                    showMessage(data.message, 'success');
                } else {
                    showMessage('导入失败: ' + data.error, 'error');
                }
            })
            .catch(error => {
                showMessage('导入失败: ' + error.message, 'error');
                console.error('导入错误:', error);
            });
        }

        function importRuleToPG() {
            // 获取所有选中的规则ID和对应的visit_type
            const selectedRules = [];
            $('.rule-checkbox:checked').each(function() {
                const row = $(this).closest('tr');
                const cells = row.find('td');
                selectedRules.push({
                    id: $(this).val(),
                    visit_type: cells.eq(6).text().trim(), // 使用第7列(index=6)的数据作为visit_type
                    city: cells.eq(4).text().trim() // 获取城市信息
                });
            });
            
            if (selectedRules.length === 0) {
                showMessage('请至少选择一条规则', 'warning');
                return;
            }

            // 检查是否所有规则都来自同一个城市
            const cities = [...new Set(selectedRules.map(rule => rule.city))];
            if (cities.length > 1) {
                showMessage('请选择来自同一个城市的规则进行批量导入', 'warning');
                return;
            }

            // 获取城市配置
            const cityName = cities[0];
            const cityConfig = getCityConfig(cityName);
            const hospitalCode = generateHospitalCode(cityConfig.city_id);
            const hospitalName = generateHospitalName(cityName);

            // 显示配置确认对话框
            const configMessage = `
导入配置信息：
- 省份ID: ${cityConfig.province_id}
- 省份名称: ${cityConfig.province_name}
- 城市ID: ${cityConfig.city_id}
- 城市名称: ${cityName}
- 医院编码: ${hospitalCode}
- 医院名称: ${hospitalName}

确定要导入 ${selectedRules.length} 条规则的SQL到PG数据库吗？
            `;

            if (!confirm(configMessage)) {
                return;
            }

            // 显示加载状态
            const importBtn = $('#importRuleToPGBtn');
            const originalText = importBtn.html();
            importBtn.html('<i class="bi bi-hourglass-split"></i> 导入中...').prop('disabled', true);

            // 按visit_type分组规则
            const rulesByVisitType = selectedRules.reduce((acc, rule) => {
                if (!acc[rule.visit_type]) {
                    acc[rule.visit_type] = [];
                }
                acc[rule.visit_type].push(rule.id);
                return acc;
            }, {});

            // 为每个visit_type组发送一个请求
            const importPromises = Object.entries(rulesByVisitType).map(([visit_type, rule_ids]) => {
                return fetch('/api/import_rule_to_pg', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        rule_ids: rule_ids,
                        visit_type: visit_type,
                        city_config: {
                            province_id: cityConfig.province_id,
                            province_name: cityConfig.province_name,
                            city_id: cityConfig.city_id,
                            city_name: cityName,
                            hospital_code: hospitalCode,
                            hospital_name: hospitalName
                        }
                    })
                }).then(response => response.json());
            });

            // 处理所有请求的结果
            Promise.all(importPromises)
                .then(results => {
                    const successCount = results.reduce((count, data) => {
                        if (data.success) {
                            const importedCount = parseInt(data.message.match(/\d+/)[0]);
                            return count + importedCount;
                        }
                        return count;
                    }, 0);

                    if (successCount > 0) {
                        showMessage(`成功导入 ${successCount} 条规则的SQL内容`, 'success');
                        // 重置选中状态
                        document.getElementById('selectAll').checked = false;
                        $('.rule-checkbox').prop('checked', false);
                        updateSelectedCount();
                    } else {
                        throw new Error('没有规则被成功导入');
                    }
                })
                .catch(error => {
                    console.error('导入失败:', error);
                    showMessage('导入失败：' + error.message, 'error');
                })
                .finally(() => {
                    // 恢复按钮状态
                    importBtn.html(originalText).prop('disabled', false);
                });
        }

        function autoFillConfig() {
            // 获取当前选中的规则
            const selectedCheckbox = document.querySelector('#rulesTable tbody tr td input[type="checkbox"]:checked');
            if (!selectedCheckbox) {
                showMessage('请先选择一条规则', 'warning');
                return;
            }

            // 获取选中规则的城市信息
            const selectedRow = selectedCheckbox.closest('tr');
            const cityCell = selectedRow.querySelector('td:nth-child(5)'); // 第5列是城市
            const cityName = cityCell.textContent.trim();

            // 根据城市信息自动填充配置
            const cityConfig = getCityConfig(cityName);
            const hospitalCode = generateHospitalCode(cityConfig.city_id);
            const hospitalName = generateHospitalName(cityName);

            // 填充表单
            document.querySelector('input[name="province_id"]').value = cityConfig.province_id;
            document.querySelector('input[name="province_name"]').value = cityConfig.province_name;
            document.querySelector('input[name="city_id"]').value = cityConfig.city_id;
            document.querySelector('input[name="city_name"]').value = cityName;
            document.querySelector('input[name="hospital_code"]').value = hospitalCode;
            document.querySelector('input[name="hospital_name"]').value = hospitalName;

            // 更新配置预览
            updateConfigPreview();

            showMessage(`已根据城市"${cityName}"自动填充配置信息`, 'success');
        }

        function showDbConfigModal() {
            fetch('/api/pg_config')
                .then(response => response.json())
                .then(data => {
                    const form = document.getElementById('dbConfigForm');
                    form.host.value = data.host || '';
                    form.port.value = data.port || '5432';
                    form.dbname.value = data.dbname || '';
                    form.user.value = data.user || '';
                    form.password.value = data.password || '';
                    form.schema.value = data.schema || 'public';
                    new bootstrap.Modal(document.getElementById('dbConfigModal')).show();
                })
                .catch(error => {
                    showMessage('读取数据库配置失败: ' + error, 'error');
                });
        }

        function saveDbConfig() {
            const form = document.getElementById('dbConfigForm');
            const data = {
                host: form.host.value,
                port: form.port.value,
                dbname: form.dbname.value,
                user: form.user.value,
                password: form.password.value,
                schema: form.schema.value
            };
            fetch('/api/pg_config', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(res => {
                if (res.success) {
                    showMessage('数据库配置已保存', 'success');
                    bootstrap.Modal.getInstance(document.getElementById('dbConfigModal')).hide();
                } else {
                    showMessage('保存失败: ' + (res.message || '未知错误'), 'error');
                }
            })
            .catch(error => {
                showMessage('保存失败: ' + error, 'error');
            });
        }

        // 检查URL参数并自动过滤
        function checkUrlParameters() {
            const urlParams = new URLSearchParams(window.location.search);
            const ruleId = urlParams.get('ruleId');

            if (ruleId) {
                // 设置规则ID输入框的值
                document.getElementById('ruleIdInput').value = ruleId;

                // 延迟执行搜索，确保表格已经初始化完成
                setTimeout(() => {
                    searchRules();
                    // 显示提示信息
                    showMessage(`已自动过滤规则ID: ${ruleId}`, 'info');
                }, 1000);
            }
        }
    </script>
</body>
</html>